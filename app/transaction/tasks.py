from business.models import Business
from core.celery import APP
from fees.handlers.credit_commission_handler import ProcessCommissionHandler
from fees.handlers.fee_processor_handler import FeeProcessorHandler
from transaction.models import Transaction


@APP.task()
def process_commission(txn_id, business_id):
    txn = Transaction.objects.filter(pk=txn_id).first()
    if not txn:
        return "Invalid transaction"

    business = Business.objects.filter(pk=business_id).first()
    if not business:
        return "Invalid business"

    values = ProcessCommissionHandler().handle(txn, business)

    return values


@APP.task()
def handle_service_revenue(txn_id, business_id):
    txn = Transaction.objects.filter(pk=txn_id).first()
    if not txn:
        return "Invalid transaction"

    business = Business.objects.filter(pk=business_id).first()
    if not business:
        return "Invalid business"

    fee_handler = FeeProcessorHandler(
        amount=txn.amount,
        product=txn.txn_class.capitalize(),
        service=txn.type.capitalize(),
        business=business,
        provider=txn.provider,
    )

    provider_value = fee_handler.get_provider_fee()
    revenue = txn.net_amount - provider_value

    txn.revenue = revenue
    txn.save()
