from dataclasses import dataclass
from decimal import Decimal
from typing import Optional

from business.models import Business
from wallet.models import Wallet


@dataclass
class CreateBaseTransactionParams:
    wallet: Wallet
    business: Business
    amount: Decimal
    charge: Decimal
    txn_class: str
    type: str
    narration: str
    provider: str
    merchant_reference: Optional[str] = None
