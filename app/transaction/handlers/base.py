from decimal import Decimal
from typing import Any

from common.enums import CoreServiceResponseStatus
from django.db import transaction
from ledger.enums import LedgerTypeEnum
from ledger.models import LedgerTransaction
from ledger.service import LedgerService
from transaction.enums import TransactionClassEnum, TransactionStatusEnum
from transaction.exceptions import TransactionException
from transaction.models.base import Transaction
from wallet.models import Wallet
from wallet.service import WalletService

from ..dtos import CreateBaseTransactionParams
from .airtime import AirtimeVASTransactionHandler
from .betting import BettingVASTransactionHandler
from .cable_tv import CableTVVASTransactionHandler
from .data import DataVASTransactionHandler
from .education import EducationVASTransactionHandler
from .electricity import ElectricityVASTransactionHandler
from .epin import EpinVASTransactionHandler
from .interface import BaseVASTransactionHandler
from .kyc import KYCVASTransactionHandler
from .transfer import FundsTransferTransactionHandler
from .virtual_account import VirtualAccountVASTransactionHandler

VAS_HANDLER_MAP = {
    TransactionClassEnum.AIRTIME.value: AirtimeVASTransactionHandler(),
    TransactionClassEnum.CABLE_TV.value: CableTVVASTransactionHandler(),
    TransactionClassEnum.BETTING.value: BettingVASTransactionHandler(),
    TransactionClassEnum.DATA.value: DataVASTransactionHandler(),
    TransactionClassEnum.EDUCATION.value: EducationVASTransactionHandler(),
    TransactionClassEnum.ELECTRICITY.value: ElectricityVASTransactionHandler(),
    TransactionClassEnum.EPIN.value: EpinVASTransactionHandler(),
    TransactionClassEnum.KYC.value: KYCVASTransactionHandler(),
    TransactionClassEnum.VIRTUAL_ACCOUNT.value: VirtualAccountVASTransactionHandler(),
    TransactionClassEnum.TRANSFER.value: FundsTransferTransactionHandler(),
}


class TransactionHandler:
    @staticmethod
    def _get_vas_handler(txn_class: TransactionClassEnum) -> BaseVASTransactionHandler:
        try:
            return VAS_HANDLER_MAP[txn_class]
        except KeyError:
            raise TransactionException(f"No VAS transaction handler for: {txn_class}")

    @staticmethod
    def debit_wallet(params: CreateBaseTransactionParams) -> Transaction:
        txn = WalletService(params.wallet).debit(
            params.amount,
            params.charge,
            business=params.business,
            txn_class=params.txn_class,
            type=params.type,
            narration=params.narration,
            merchant_reference=params.merchant_reference,
            provider=params.provider,
        )
        return txn

    def update_base_transaction_status(
        self, txn: Transaction, status: CoreServiceResponseStatus
    ) -> Transaction:
        match status:
            case CoreServiceResponseStatus.Pending.value:
                status = TransactionStatusEnum.PENDING.value
            case CoreServiceResponseStatus.Success.value:
                status = TransactionStatusEnum.SUCCESSFUL.value
            case CoreServiceResponseStatus.Failed.value:
                status = TransactionStatusEnum.FAILED.value
        txn.status = status
        txn.save()
        return txn

    def create_vas_transaction_and_ledger_entry(
        self, txn: Transaction, txn_class: TransactionClassEnum, extra_fields: dict
    ) -> tuple[Any, LedgerTransaction]:
        with transaction.atomic():
            vas_handler = self._get_vas_handler(txn_class)
            vas_txn = vas_handler.create_vas_transaction(txn, extra_fields)
            ledger_txn = vas_handler.create_ledger_entry(txn)
            return vas_txn, ledger_txn

    def update_vas_transaction(
        self, txn: Transaction, vas_txn: Any, update_fields: dict
    ) -> Any:
        vas_handler = self._get_vas_handler(txn.txn_class)
        return vas_handler.update_vas_transaction(vas_txn, update_fields)

    def reverse_transaction(
        self,
        txn: Transaction,
        vas_txn: Any,
        wallet: Wallet,
        ledger_type: LedgerTypeEnum,
    ):
        with transaction.atomic():
            txn.status = TransactionStatusEnum.FAILED.value
            txn.is_reversed = True
            txn.save()

            self.update_vas_transaction(txn, vas_txn, {"status": txn.status})

            wallet = Wallet.objects.filter(pk=wallet.id).select_for_update().first()
            wallet.credit(Decimal(txn.net_amount))

            LedgerService().debit(ledger_type=ledger_type, source_transaction=txn)
