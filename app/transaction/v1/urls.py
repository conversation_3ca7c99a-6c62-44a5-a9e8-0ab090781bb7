from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .views import (
    CommissionTransactionOverviewAPIView,
    TailRecentTransactionsView,
    TransactionOverviewAPIView,
    TransactionViewSet,
)

app_name = "transaction"

router = DefaultRouter()
router.register("", TransactionViewSet, basename="transaction")

urlpatterns = [
    path(
        "overview/", TransactionOverviewAPIView.as_view(), name="transaction-overview"
    ),
    path(
        "tail/",
        TailRecentTransactionsView.as_view(),
        name="tail-recent-transactions",
    ),
    path(
        "commission-overview/",
        CommissionTransactionOverviewAPIView.as_view(),
        name="commission-transaction-overview",
    ),
    path("", include(router.urls)),
]
