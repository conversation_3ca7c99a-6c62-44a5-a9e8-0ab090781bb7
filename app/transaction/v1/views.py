from decimal import Decimal

from common.pagination import LargeDatasetKeySetPagination
from common.permissions import IsAdmin
from django.db.models import Count, Q, Sum
from django.db.models.functions import Coalesce
from django.http import Http404
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import OpenApiParameter, OpenApiTypes, extend_schema
from rest_framework import filters, generics, status, viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from teams.enums import SystemBaseUserRole
from transaction.enums import TransactionClassEnum, TransactionStatusEnum
from transaction.models import CommissionTransaction, Transaction
from transaction.v1.filters import (
    CommissionTransactionOverviewFilter,
    TransactionFilter,
    TransactionOverviewFilter,
)
from transaction.v1.serializers import (
    CommissionTransactionOverviewSerializer,
    TailTransactionSerializer,
    TransactionOverviewSerializer,
    TransactionPolymorphicSerializer,
    TransactionSerializer,
)


class TransactionViewSet(viewsets.ModelViewSet):
    queryset = Transaction.objects.all().select_related("wallet", "business")
    permission_classes = [IsAuthenticated]
    serializer_class = TransactionSerializer
    http_method_names = ["get"]
    lookup_field = "identifier"
    pagination_class = LargeDatasetKeySetPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = TransactionFilter
    search_fields = ["reference", "merchant_reference"]
    ordering_fields = ["-created_at"]

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == SystemBaseUserRole.BusinessOwner.value:
            return queryset.filter(business__owner=self.request.user)
        return queryset

    def get_object(self):
        lookup_value = self.kwargs.get(self.lookup_field)
        for field in ["id", "reference", "merchant_reference"]:
            try:
                return self.get_queryset().get(**{field: lookup_value})
            except Transaction.DoesNotExist:
                continue

        raise Http404("Transaction not found.")

    def get_serializer_class(self):
        if self.action == "retrieve":
            return TransactionPolymorphicSerializer
        return super().get_serializer_class()


class TransactionOverviewAPIView(generics.GenericAPIView):
    queryset = Transaction.objects.all().select_related("wallet", "business")
    serializer_class = TransactionOverviewSerializer
    permission_classes = [IsAuthenticated]
    filterset_class = TransactionOverviewFilter
    filter_backends = [DjangoFilterBackend]

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == SystemBaseUserRole.BusinessOwner.value:
            return queryset.filter(business__owner=self.request.user)
        return queryset

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="start",
                required=False,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
                description="Start date in format YYYY-MM-DD",
            ),
            OpenApiParameter(
                name="end",
                required=False,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
                description="End date in format YYYY-MM-DD",
            ),
            OpenApiParameter(
                name="txn_class",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                enum=TransactionClassEnum.values(),
                description="Transaction Class",
            ),
        ],
        responses={200: TransactionOverviewSerializer},
    )
    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        filtered_queryset = self.filter_queryset(queryset)
        totals = filtered_queryset.aggregate(
            total_value=Sum("amount"),
            total_count=Count("id"),
            pending_count=Count(
                "id", filter=Q(status=TransactionStatusEnum.PENDING.value)
            ),
            failed_count=Count(
                "id", filter=Q(status=TransactionStatusEnum.FAILED.value)
            ),
            successful_count=Count(
                "id", filter=Q(status=TransactionStatusEnum.SUCCESSFUL.value)
            ),
        )

        response_data = {
            "total_value": totals.get("total_value", 0) or 0,
            "total_count": totals.get("total_count", 0),
            "pending_count": totals.get("pending_count", 0),
            "failed_count": totals.get("failed_count", 0),
            "successful_count": totals.get("successful_count", 0),
        }
        return Response(response_data, status=status.HTTP_200_OK)


class CommissionTransactionOverviewAPIView(generics.GenericAPIView):
    queryset = CommissionTransaction.objects.all().select_related("wallet", "business")
    serializer_class = TransactionOverviewSerializer
    permission_classes = [IsAuthenticated]
    filterset_class = CommissionTransactionOverviewFilter
    filter_backends = [DjangoFilterBackend]

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == SystemBaseUserRole.BusinessOwner.value:
            return queryset.filter(business__owner=self.request.user)
        return queryset

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="start",
                required=False,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
                description="Start date in format YYYY-MM-DD",
            ),
            OpenApiParameter(
                name="end",
                required=False,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
                description="End date in format YYYY-MM-DD",
            ),
            OpenApiParameter(
                name="txn_class",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                enum=TransactionClassEnum.values(),
                description="Transaction Class",
            ),
        ],
        responses={200: CommissionTransactionOverviewSerializer},
    )
    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        filtered_queryset = self.filter_queryset(queryset)
        commission_totals = filtered_queryset.aggregate(
            total_commission=Coalesce(Sum("amount"), Decimal("0.00")),
            total_commission_count=Coalesce(Count("id"), 0),
        )

        return Response(
            {
                "total_commission": commission_totals["total_commission"],
                "total_commission_count": commission_totals["total_commission_count"],
            }
        )


class TailRecentTransactionsView(generics.GenericAPIView):
    serializer_class = TailTransactionSerializer
    permission_classes = [IsAdmin]
    filter_backends = [DjangoFilterBackend]
    filterset_class = TransactionFilter

    def get_queryset(self):
        return Transaction.objects.select_related("business").order_by("-created_at")

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="status",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                enum=TransactionStatusEnum.values(),
                description="Transaction Status",
            ),
            OpenApiParameter(
                name="items_size",
                required=False,
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="Item Size. Default: 10, Maximum: 30",
            ),
            OpenApiParameter(
                name="txn_class",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                enum=TransactionClassEnum.values(),
                description="Transaction Class",
            ),
        ],
        responses={200: TailTransactionSerializer},
    )
    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        try:
            items_size = int(request.query_params.get("items_size", 10))
        except ValueError:
            items_size = 10

        items_size = max(1, min(items_size, 30))
        queryset = queryset[:items_size]

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
