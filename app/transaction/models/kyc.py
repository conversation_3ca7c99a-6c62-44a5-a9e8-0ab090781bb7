from common.enums import KYCType<PERSON>num
from django.db import models

from .base import VASTransaction


class KYCVASTransaction(VASTransaction):
    type = models.CharField(max_length=20, choices=KYCTypeEnum.choices(), db_index=True)
    identity = models.Char<PERSON><PERSON>(max_length=30, db_index=True, null=True, blank=True)
    full_name = models.CharField(max_length=255, null=True, blank=True)
    phone = models.CharField(max_length=20, null=True, blank=True)
    gender = models.CharField(max_length=10, null=True, blank=True)
    dob = models.CharField(max_length=30, null=True, blank=True)

    class Meta:
        db_table = "kyc_vas_transaction"
        verbose_name = "KYC VAS Transaction"
        verbose_name_plural = "KYC VAS Transactions"

    def __str__(self):
        return f"{self.reference} - {self.type}"
