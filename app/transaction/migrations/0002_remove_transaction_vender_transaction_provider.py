# Generated by Django 5.1.7 on 2025-06-24 19:09

import common.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("transaction", "0001_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="transaction",
            name="vender",
        ),
        migrations.AddField(
            model_name="transaction",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=common.enums.ProviderEnum.choices,
                db_index=True,
                max_length=50,
                null=True,
            ),
        ),
    ]
