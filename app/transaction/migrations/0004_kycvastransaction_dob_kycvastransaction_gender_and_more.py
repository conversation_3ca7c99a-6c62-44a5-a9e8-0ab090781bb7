# Generated by Django 5.1.7 on 2025-06-25 11:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("transaction", "0003_transaction_is_reversed"),
    ]

    operations = [
        migrations.AddField(
            model_name="kycvastransaction",
            name="dob",
            field=models.Char<PERSON>ield(blank=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name="kycvastransaction",
            name="gender",
            field=models.Char<PERSON>ield(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="kycvastransaction",
            name="identity",
            field=models.Char<PERSON>ield(blank=True, db_index=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name="kycvastransaction",
            name="identity_full_name",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="kycvastransaction",
            name="phone",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=20, null=True),
        ),
    ]
