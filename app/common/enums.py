from enum import Enum

from core import settings


class CustomEnum(Enum):
    @classmethod
    def values(cls):
        return [c.value for c in cls]

    @classmethod
    def choices(cls):
        return [(c.value, c.value) for c in cls]


class CoreServiceResponseStatus(CustomEnum):
    Pending = "pending"
    Success = "success"
    Failed = "failed"


class AirtimeNetworkEnum(CustomEnum):
    MTN = "MTN"
    GLO = "GLO"
    AIRTEL = "AIRTEL"
    ETISALAT = "9MOBILE"


class DataNetworkEnum(CustomEnum):
    MTN = "MTN"
    GLO = "GLO"
    AIRTEL = "AIRTEL"
    ETISALAT = "9MOBILE"


class CableTVBillerEnum(CustomEnum):
    DSTV = "DSTV"
    GOTV = "GOTV"
    STARTIMES = "STARTIMES"


class CableTVBillerIDEnum(CustomEnum):
    GOTV = "1"
    DSTV = "2"
    STARTIMES = "3"


class KYCTypeEnum(CustomEnum):
    BVN = "BVN"
    NIN = "NIN"
    PHONE_NUMBER = "PHONE_NUMBER"


class BetBillerEnum(CustomEnum):
    Bet9ja = "Bet9ja"
    BangBet = "BangBet"
    NairaBet = "SupaBet"
    CloudBet = "CloudBet"
    BetLion = "BetLion"
    OneXBet = "1xBet"
    MerryBet = "MerryBet"
    BetWay = "BetWay"
    BetLand = "BetLand"
    BetKing = "BetKing"
    LiveScoreBet = "LiveScoreBet"
    NaijaBet = "NaijaBet"


class ElectricityDiscoTypeEnum(CustomEnum):
    PREPAID = "PREPAID"
    POSTPAID = "POSTPAID"


class ElectricityDiscoEnum(CustomEnum):
    AbujaElectric = "AbujaElectric"
    BeninElectric = "BeninElectric"
    EnuguElectric = "EnuguElectric"
    EkoElectric = "EkoElectric"
    IbadanElectric = "IbadanElectric"
    IkejaElectric = "IkejaElectric"
    JosElectric = "JosElectric"
    PortharcourtElectric = "PortharcourtElectric"
    KadunaElectric = "KadunaElectric"
    KanoElectric = "KanoElectric"
    YolaElectric = "YolaElectric"


class ServiceEnum(CustomEnum):
    # Data, Airtime, Epin
    Mtn = "Mtn"
    Glo = "Glo"
    Airtel = "Airtel"
    Etisalat = "9Mobile"

    # Cable-tv
    Dstv = "Dstv"
    Gotv = "Gotv"
    Startimes = "Startimes"

    # Electricity
    AbujaElectric = "AbujaElectric"
    BeninElectric = "BeninElectric"
    EnuguElectric = "EnuguElectric"
    EkoElectric = "EkoElectric"
    IbadanElectric = "IbadanElectric"
    IkejaElectric = "IkejaElectric"
    JosElectric = "JosElectric"
    PortharcourtElectric = "PortharcourtElectric"
    KadunaElectric = "KadunaElectric"
    KanoElectric = "KanoElectric"
    YolaElectric = "YolaElectric"

    # Betting
    Bet9ja = "Bet9ja"
    BangBet = "BangBet"
    NairaBet = "SupaBet"
    CloudBet = "CloudBet"
    BetLion = "BetLion"
    OneXBet = "1xBet"
    MerryBet = "MerryBet"
    BetWay = "BetWay"
    BetLand = "BetLand"
    BetKing = "BetKing"
    LiveScoreBet = "LiveScoreBet"
    NaijaBet = "NaijaBet"

    # Kyc
    Nin = "Nin"
    Bvn = "Bvn"
    PhoneNumberLookup = "PhoneNumberLookup"

    # Education
    Waec = "Waec"
    Jamb = "Jamb"

    # Transfer
    Transfer = "Transfer"

    # VirtualAccount
    VirtualAccount = "VirtualAccount"

    # RecurringDebit
    RecurringDebit = "RecurringDebit"


class ElectricityBiller(str, CustomEnum):
    AbujaPostpaid = "abuja_electric_postpaid"
    AbujaPrepaid = "abuja_electric_prepaid"
    BeninPostpaid = "benin_electric_postpaid"
    BeninPrepaid = "benin_electric_prepaid"
    EnuguPrepaid = "enugu_electric_prepaid"
    EnuguPostpaid = "enugu_electric_postpaid"
    EkoPrepaid = "eko_electric_prepaid"
    EkoPostpaid = "eko_electric_postpaid"
    IbadanPrepaid = "ibadan_electric_prepaid"
    IbadanPostPaid = "ibadan_electric_postpaid"
    IkejaPostpaid = "ikeja_electric_postpaid"
    IkejaPrepaid = "ikeja_electric_prepaid"
    JosPrepaid = "jos_electric_prepaid"
    JosPostpaid = "jos_electric_postpaid"
    PortharcourtPrepaid = "portharcourt_electric_prepaid"
    PortharcourtPostpaid = "portharcourt_electric_postpaid"
    KadunaPrepaid = "kaduna_electric_prepaid"
    KadunaPostpaid = "kaduna_electric_postpaid"
    KanoPrepaid = "kedco_electric_prepaid"
    KanoPostpaid = "kedco_electric_postpaid"
    YolaPrepaid = "yola_electric_prepaid"
    YolaPostpaid = "yola_electric_postpaid"

    def title(self) -> str:
        return {
            self.AbujaPostpaid: "Abuja Electric (Postpaid)",
            self.AbujaPrepaid: "Abuja Electric (Prepaid)",
            self.BeninPostpaid: "Benin Electric (Postpaid)",
            self.BeninPrepaid: "Benin Electric (Prepaid)",
            self.EnuguPrepaid: "Enugu Electric (Prepaid)",
            self.EnuguPostpaid: "Enugu Electric (Postpaid)",
            self.EkoPrepaid: "Eko Electric (Prepaid)",
            self.EkoPostpaid: "Eko Electric (Postpaid)",
            self.IbadanPrepaid: "Ibadan Electric (Prepaid)",
            self.IbadanPostPaid: "Ibadan Electric (Postpaid)",
            self.IkejaPostpaid: "Ikeja Electric (Postpaid)",
            self.IkejaPrepaid: "Ikeja Electric (Prepaid)",
            self.JosPrepaid: "Jos Electric (Prepaid)",
            self.JosPostpaid: "Jos Electric (Postpaid)",
            self.KadunaPostpaid: "Kaduna Electric (Postpaid)",
            self.KadunaPrepaid: "Kaduna Electric (Prepaid)",
            self.KanoPrepaid: "Kano Electric (Prepaid)",
            self.KanoPostpaid: "Kano Electric (Postpaid)",
            self.PortharcourtPrepaid: "Port Electric Harcourt (Prepaid)",
            self.PortharcourtPostpaid: "Port Electric Harcourt (Postpaid)",
            self.YolaPrepaid: "Yola Electric (Prepaid)",
            self.YolaPostpaid: "Yola Electric (Postpaid)",
        }[self]

    def service(self) -> ServiceEnum:
        mapping = {
            ElectricityBiller.AbujaPrepaid: ServiceEnum.AbujaElectric,
            ElectricityBiller.AbujaPostpaid: ServiceEnum.AbujaElectric,
            ElectricityBiller.BeninPrepaid: ServiceEnum.BeninElectric,
            ElectricityBiller.BeninPostpaid: ServiceEnum.BeninElectric,
            ElectricityBiller.EnuguPrepaid: ServiceEnum.EnuguElectric,
            ElectricityBiller.EnuguPostpaid: ServiceEnum.EnuguElectric,
            ElectricityBiller.EkoPrepaid: ServiceEnum.EkoElectric,
            ElectricityBiller.EkoPostpaid: ServiceEnum.EkoElectric,
            ElectricityBiller.IbadanPrepaid: ServiceEnum.IbadanElectric,
            ElectricityBiller.IbadanPostPaid: ServiceEnum.IbadanElectric,
            ElectricityBiller.IkejaPrepaid: ServiceEnum.IkejaElectric,
            ElectricityBiller.IkejaPostpaid: ServiceEnum.IkejaElectric,
            ElectricityBiller.JosPrepaid: ServiceEnum.JosElectric,
            ElectricityBiller.JosPostpaid: ServiceEnum.JosElectric,
            ElectricityBiller.PortharcourtPrepaid: ServiceEnum.PortharcourtElectric,
            ElectricityBiller.PortharcourtPostpaid: ServiceEnum.PortharcourtElectric,
            ElectricityBiller.KadunaPrepaid: ServiceEnum.KadunaElectric,
            ElectricityBiller.KadunaPostpaid: ServiceEnum.KadunaElectric,
            ElectricityBiller.KanoPrepaid: ServiceEnum.KanoElectric,
            ElectricityBiller.KanoPostpaid: ServiceEnum.KanoElectric,
            ElectricityBiller.YolaPrepaid: ServiceEnum.YolaElectric,
            ElectricityBiller.YolaPostpaid: ServiceEnum.YolaElectric,
        }

        return mapping[self]

    @property
    def disco(self) -> str:
        # e.g., "abuja_electric_postpaid" → "ABUJA"
        return self.split("_")[0].upper()

    @property
    def disco_type(self) -> str:
        # e.g., "abuja_electric_postpaid" → "POSTPAID"
        return self.split("_")[-1].upper()


class VasGateStatus(CustomEnum):
    Success = "success"
    Failed = "failed"
    Pending = "pending"


class VABankProvider(CustomEnum):
    Access = "access"
    Wema = "wema"
    Kolomoni = "kolomoni"

    def bank_code(self):
        return {self.Access: "044", self.Wema: "035", self.Kolomoni: "090480"}[self]

    def bank_name(self):
        return {
            self.Access: "ACCESS BANK",
            self.Wema: "WEMA BANK",
            self.Kolomoni: "KOLOMONI MFB",
        }[self]

    def account_prefix(self):
        return {
            self.Wema: settings.WEMA_VA_ACCOUNT_PREFIX,
            self.Kolomoni: settings.KOLOMONI_VA_ACCOUNT_PREFIX,
        }[self]


class EducationProviderEnum(CustomEnum):
    Waec = "Waec"
    Jamb = "Jamb"


class EducationServiceTypeEnum(CustomEnum):
    WAEC_RESULT_CHECKER = "WAEC_RESULT_CHECKER"
    JAMB_RESULT_CHECKER = "JAMB_RESULT_CHECKER"
    JAMB_FORM = "JAMB_FORM"


class WaecExamTypeEnum(CustomEnum):
    WASSCE = "WASSCE"
    GCE = "GCE"


class JambExamTypeEnum(CustomEnum):
    UTME = "UTME"
    DE = "DE"


class EpinNetwork(CustomEnum):
    Mtn = "MTN"
    Glo = "GLO"
    Airtel = "AIRTEL"
    Etisalat = "9MOBILE"


class EpinAmount(CustomEnum):
    OneHundred = 100
    TwoHundred = 200
    FiveHundred = 500
    OneThousand = 1000


class ProductEnum(CustomEnum):
    Transfer = "Transfer"
    Airtime = "Airtime"
    Data = "Data"
    Kyc = "Kyc"
    VirtualAccount = "VirtualAccount"
    RecurringDebit = "RecurringDebit"
    CableTv = "CableTv"
    Epin = "Epin"
    Education = "Education"
    Electricity = "Electricity"
    Betting = "Betting"


class ProviderEnum(CustomEnum):
    Shago = "Shago"
    Sonite = "Sonite"
    Quickteller = "Quickteller"
    Glo = "Glo"
    YouVerify = "YouVerify"
    Blusalt = "Blusalt"
    Access = "Access"
    Jamb = "Jamb"
    EasyPayWema = "EasyPayWema"
    EasyPayZenith = "EasyPayZenith"
    EasyPayFidelity = "EasyPayFidelity"
    EasyPayEcobank = "EasyPayEcobank"
    MamaAfrica = "MamaAfrica"
    Dojah = "Dojah"
    Wema = "Wema"
    Kolomoni = "Kolomoni"

    def services(self):

        service_map = {
            self.MamaAfrica: [
                (ProductEnum.Epin, ServiceEnum.Mtn),
                (ProductEnum.Epin, ServiceEnum.Glo),
                (ProductEnum.Epin, ServiceEnum.Etisalat),
                (ProductEnum.Epin, ServiceEnum.Airtel),
            ],
            self.Glo: [
                (ProductEnum.Airtime, ServiceEnum.Glo),
                (ProductEnum.Data, ServiceEnum.Glo),
            ],
            self.Sonite: [
                (ProductEnum.Airtime, ServiceEnum.Mtn),
                (ProductEnum.Airtime, ServiceEnum.Glo),
                (ProductEnum.Airtime, ServiceEnum.Etisalat),
                (ProductEnum.Airtime, ServiceEnum.Airtel),
                (ProductEnum.Data, ServiceEnum.Mtn),
                (ProductEnum.Data, ServiceEnum.Glo),
                (ProductEnum.Data, ServiceEnum.Etisalat),
                (ProductEnum.Data, ServiceEnum.Airtel),
            ],
            self.Shago: [
                (ProductEnum.Airtime, ServiceEnum.Mtn),
                (ProductEnum.Airtime, ServiceEnum.Glo),
                (ProductEnum.Airtime, ServiceEnum.Etisalat),
                (ProductEnum.Airtime, ServiceEnum.Airtel),
                (ProductEnum.Data, ServiceEnum.Mtn),
                (ProductEnum.Data, ServiceEnum.Glo),
                (ProductEnum.Data, ServiceEnum.Etisalat),
                (ProductEnum.Data, ServiceEnum.Airtel),
                (ProductEnum.CableTv, ServiceEnum.Dstv),
                (ProductEnum.CableTv, ServiceEnum.Gotv),
                (ProductEnum.CableTv, ServiceEnum.Startimes),
                (ProductEnum.Electricity, ServiceEnum.IkejaElectric),
                (ProductEnum.Electricity, ServiceEnum.AbujaElectric),
                (ProductEnum.Electricity, ServiceEnum.EkoElectric),
                (ProductEnum.Electricity, ServiceEnum.EnuguElectric),
                (ProductEnum.Electricity, ServiceEnum.BeninElectric),
                (ProductEnum.Electricity, ServiceEnum.KanoElectric),
                (ProductEnum.Electricity, ServiceEnum.KadunaElectric),
                (ProductEnum.Electricity, ServiceEnum.IbadanElectric),
                (ProductEnum.Electricity, ServiceEnum.PortharcourtElectric),
                (ProductEnum.Electricity, ServiceEnum.JosElectric),
                (ProductEnum.Electricity, ServiceEnum.YolaElectric),
                (ProductEnum.Betting, ServiceEnum.Bet9ja),
                (ProductEnum.Betting, ServiceEnum.NairaBet),
                (ProductEnum.Betting, ServiceEnum.CloudBet),
                (ProductEnum.Betting, ServiceEnum.BetLion),
                (ProductEnum.Betting, ServiceEnum.OneXBet),
                (ProductEnum.Betting, ServiceEnum.MerryBet),
                (ProductEnum.Betting, ServiceEnum.BetWay),
                (ProductEnum.Betting, ServiceEnum.BetLand),
                (ProductEnum.Betting, ServiceEnum.LiveScoreBet),
                (ProductEnum.Betting, ServiceEnum.NaijaBet),
                (ProductEnum.Education, ServiceEnum.Waec),
            ],
            self.Quickteller: [
                (ProductEnum.Electricity, ServiceEnum.IkejaElectric),
                (ProductEnum.Electricity, ServiceEnum.AbujaElectric),
                (ProductEnum.Electricity, ServiceEnum.EkoElectric),
                (ProductEnum.Electricity, ServiceEnum.EnuguElectric),
                (ProductEnum.Electricity, ServiceEnum.BeninElectric),
                (ProductEnum.Electricity, ServiceEnum.KanoElectric),
                (ProductEnum.Electricity, ServiceEnum.KadunaElectric),
                (ProductEnum.Electricity, ServiceEnum.IbadanElectric),
                (ProductEnum.Electricity, ServiceEnum.PortharcourtElectric),
                (ProductEnum.Electricity, ServiceEnum.JosElectric),
                (ProductEnum.Electricity, ServiceEnum.YolaElectric),
            ],
            self.YouVerify: [
                (ProductEnum.Kyc, ServiceEnum.Bvn),
                (ProductEnum.Kyc, ServiceEnum.Nin),
                (ProductEnum.Kyc, ServiceEnum.PhoneNumberLookup),
            ],
            self.Blusalt: [
                (ProductEnum.Kyc, ServiceEnum.Bvn),
                (ProductEnum.Kyc, ServiceEnum.Nin),
            ],
            self.Jamb: [
                (ProductEnum.Education, ServiceEnum.Jamb),
            ],
            self.Dojah: [
                (ProductEnum.Kyc, ServiceEnum.PhoneNumberLookup),
                (ProductEnum.Kyc, ServiceEnum.Nin),
                (ProductEnum.Kyc, ServiceEnum.Bvn),
            ],
            self.EasyPayWema: [
                (ProductEnum.Transfer, ServiceEnum.Transfer),
            ],
            self.EasyPayZenith: [
                (ProductEnum.Transfer, ServiceEnum.Transfer),
            ],
            self.EasyPayFidelity: [
                (ProductEnum.Transfer, ServiceEnum.Transfer),
            ],
            self.EasyPayEcobank: [
                (ProductEnum.Transfer, ServiceEnum.Transfer),
            ],
            self.Wema: [
                (ProductEnum.Transfer, ServiceEnum.Transfer),
            ],
            # Add other vendors similarly...
        }

        return tuple(
            (product.value, provider.value)
            for product, provider in service_map.get(self, [])
        )
