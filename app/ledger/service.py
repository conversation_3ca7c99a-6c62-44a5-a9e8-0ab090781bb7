import logging

from common.kgs import generate_uuid7
from django.db import transaction
from ledger.enums import LedgerTypeEnum
from ledger.exceptions import LedgerException
from ledger.models import Ledger, LedgerTransaction
from transaction.enums import (
    TransactionModeEnum,
)
from transaction.models import CommissionTransaction, Transaction

logger = logging.getLogger(__name__)


class LedgerService:

    @transaction.atomic
    def debit(
        self,
        ledger_type: LedgerTypeEnum,
        source_transaction: Transaction = None,
        source_commission: CommissionTransaction = None,
    ) -> LedgerTransaction:

        item = source_transaction or source_commission

        ledger = (
            Ledger.objects.select_for_update().filter(type=ledger_type.value).first()
        )
        if not ledger:
            raise LedgerException("Invalid ledger.")

        old_balance = ledger.balance
        ledger.debit(item.amount)

        new_balance = ledger.balance

        ledger_txn = LedgerTransaction.objects.create(
            ledger=ledger,
            amount=item.amount,
            narration=item.narration,
            source_transaction=source_transaction,
            source_commission=source_commission,
            wallet=item.wallet,
            old_balance=old_balance,
            new_balance=new_balance,
            mode=TransactionModeEnum.DEBIT.value,
            reference=generate_uuid7(),
        )

        return ledger_txn

    @transaction.atomic
    def credit(
        self,
        ledger_type: LedgerTypeEnum,
        source_transaction: Transaction = None,
        source_commission: CommissionTransaction = None,
    ) -> LedgerTransaction:

        item = source_transaction or source_commission

        ledger = (
            Ledger.objects.select_for_update().filter(type=ledger_type.value).first()
        )
        if not ledger:
            raise LedgerException("Invalid ledger.")

        old_balance = ledger.balance
        ledger.credit(item.amount)

        new_balance = ledger.balance

        ledger_txn = LedgerTransaction.objects.create(
            ledger=ledger,
            amount=item.amount,
            narration=item.narration,
            source_transaction=source_transaction,
            source_commission=source_commission,
            wallet=item.wallet,
            old_balance=old_balance,
            new_balance=new_balance,
            mode=TransactionModeEnum.CREDIT.value,
            reference=generate_uuid7(),
        )

        return ledger_txn
