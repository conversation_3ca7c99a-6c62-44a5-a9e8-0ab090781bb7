from common.models import AuditableModel
from django.db import models
from django.db.models import F
from ledger.enums import LedgerType<PERSON>num
from transaction.enums import TransactionModeEnum


class Ledger(AuditableModel):
    type = models.CharField(
        max_length=30, choices=LedgerTypeEnum.choices(), db_index=True
    )
    balance = models.DecimalField(max_digits=20, decimal_places=2)

    def debit(self, amount):
        self.balance = F("balance") - amount
        self.save()
        self.refresh_from_db()

    def credit(self, amount):
        self.balance = F("balance") + amount
        self.save()
        self.refresh_from_db()

    def __str__(self):
        return f"{self.type} -- {self.balance}"

    class Meta:
        ordering = ("-created_at",)


class LedgerTransaction(AuditableModel):
    ledger = models.ForeignKey(
        Ledger,
        on_delete=models.PROTECT,
        db_index=True,
        related_name="ledger_transactions",
    )
    wallet = models.ForeignKey(
        "wallet.Wallet",
        on_delete=models.PROTECT,
        db_index=True,
        related_name="wallet_ledger_transactions",
    )

    amount = models.DecimalField(max_digits=20, decimal_places=2)
    narration = models.TextField()

    source_transaction = models.ForeignKey(
        "transaction.Transaction",
        on_delete=models.PROTECT,
        db_index=True,
        related_name="source_txn_ledger_transactions",
        null=True,
        blank=True,
    )
    source_commission = models.ForeignKey(
        "transaction.CommissionTransaction",
        on_delete=models.PROTECT,
        db_index=True,
        related_name="source_commission_ledger_transactions",
        null=True,
        blank=True,
    )

    old_balance = models.DecimalField(max_digits=20, decimal_places=2)
    new_balance = models.DecimalField(max_digits=20, decimal_places=2)

    mode = models.CharField(
        max_length=10,
        choices=TransactionModeEnum.choices,
        db_index=True,
        null=True,
        blank=True,
    )

    reference = models.CharField(
        max_length=100, unique=True, db_index=True, null=True, blank=True
    )

    def __str__(self):
        txn = self.source_transaction or self.source_commission
        return f"{txn} -- {self.amount}"

    class Meta:
        ordering = ("-created_at",)
