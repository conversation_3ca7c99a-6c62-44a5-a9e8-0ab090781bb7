# Generated by Django 5.1.7 on 2025-06-25 14:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("teams", "0003_alter_teammember_status"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="adminprofile",
            name="invited_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="invited_admin_team_members",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="adminprofile",
            name="joined_at",
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name="adminprofile",
            name="status",
            field=models.CharField(
                choices=[
                    ("Invited", "Invited"),
                    ("Active", "Active"),
                    ("Deactivated", "Deactivated"),
                    ("Revoked", "Revoked"),
                ],
                db_index=True,
                default="Invited",
                max_length=20,
            ),
        ),
    ]
