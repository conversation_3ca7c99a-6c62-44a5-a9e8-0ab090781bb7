from django.conf import settings
from django.contrib.auth.hashers import make_password
from django.db import transaction
from rest_framework import serializers
from teams.enums import SystemBase<PERSON>serRole, SystemPlatform, TeamMemberStatus
from teams.models import (
    AdminProfile,
    Permission,
    Role,
    SystemRoleType,
    TeamMember,
)
from user.models import Token, User
from user.tasks import send_merchant_teams_registration_email
from user.utils import generate_token


class RoleSerializer(serializers.ModelSerializer):
    permissions = serializers.ListField(child=serializers.CharField(), write_only=True)
    permissions_list = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Role
        fields = [
            "id",
            "name",
            "description",
            "platform",
            "type",
            "permissions",
            "permissions_list",
        ]
        read_only_fields = ["platform", "type", "permissions_list"]

    def validate(self, attrs):
        request = self.context.get("request")
        user = request.user
        platform = (
            SystemPlatform.Business.value
            if user.role == "Business_Owner"
            else SystemPlatform.Admin.value
        )
        business = getattr(user, "business", None)

        name = attrs.get("name")
        instance = getattr(self, "instance", None)

        query = Role.objects.filter(name=name, platform=platform)
        if platform == SystemPlatform.Business.value:
            query = query.filter(business=business)
        else:
            query = query.filter(business__isnull=True)

        if instance:
            query = query.exclude(pk=instance.pk)

        if query.exists():
            raise serializers.ValidationError(
                {
                    "name": f"A role with the name '{name}' already exists for this platform."
                }
            )

        return attrs

    def validate_permissions(self, value):
        if not value:
            raise serializers.ValidationError("At least one permission is required.")

        valid_permissions = Permission.objects.filter(codename__in=value)
        if valid_permissions.count() != len(set(value)):
            existing = set(valid_permissions.values_list("codename", flat=True))
            missing = set(value) - existing
            raise serializers.ValidationError(
                f"Invalid permission codenames: {', '.join(missing)}"
            )

        return value

    def create(self, validated_data):
        permission_codenames = validated_data.pop("permissions")
        role = Role.objects.create(**validated_data)
        role.permissions.set(
            Permission.objects.filter(codename__in=permission_codenames)
        )
        return role

    def update(self, instance, validated_data):
        if instance.type == SystemRoleType.Default.value:
            raise serializers.ValidationError("Default roles cannot be edited.")

        permission_codenames = validated_data.pop("permissions", None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        if permission_codenames is not None:
            instance.permissions.set(
                Permission.objects.filter(codename__in=permission_codenames)
            )
        return instance

    def delete(self):
        if self.instance.type == SystemRoleType.Default.value:
            raise serializers.ValidationError("Cannot delete a default role.")
        if self.instance.team_members.exists():
            raise serializers.ValidationError(
                "Cannot delete a role assigned to team members."
            )
        self.instance.delete()

    def get_permissions_list(self, obj):
        return list(
            obj.permissions.order_by("codename").values_list("codename", flat=True)
        )


class BaseMemberInviteSerializer(serializers.Serializer):
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    phone_number = serializers.CharField()
    email = serializers.EmailField()

    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value


class TeamMemberInviteSerializer(BaseMemberInviteSerializer):
    role_id = serializers.PrimaryKeyRelatedField(
        queryset=Role.objects.filter(platform=SystemPlatform.Business.value)
    )

    @transaction.atomic
    def create(self, validated_data):
        email = validated_data["email"]
        role = validated_data["role_id"]
        user = User.objects.create(
            email=email,
            firstname=validated_data["first_name"],
            lastname=validated_data["last_name"],
            phone=validated_data["phone_number"],
            password=make_password(None),
            role=SystemBaseUserRole.TeamMember.value,
        )

        team_member = TeamMember.objects.create(
            user=user,
            role=role,
            business=self.context["business"],
            invited_by=self.context["request"].user,
            status=TeamMemberStatus.Invited.value,
        )

        token_str = generate_token(user)
        token, _ = Token.objects.update_or_create(
            user=user,
            token_type="CreateToken",
            defaults={
                "user": user,
                "token_type": "CreateToken",
                "token": token_str,
            },
        )

        email_data = {
            "first_name": user.firstname,
            "email": user.email,
            "url": f"{settings.CLIENT_URL}/verify-user/?token={token.token}",
            "token": token.token,
            "role_name": role.name,
        }
        send_merchant_teams_registration_email.delay(email_data)
        return team_member


class TeamMemberSerializer(serializers.ModelSerializer):
    role_name = serializers.CharField(source="role.name", read_only=True)
    last_login = serializers.DateTimeField(source="user.last_login", read_only=True)
    role_id = serializers.CharField(source="role.id", read_only=True)
    email = serializers.EmailField(source="user.email", read_only=True)
    first_name = serializers.CharField(source="user.firstname", read_only=True)
    last_name = serializers.CharField(source="user.lastname", read_only=True)
    phone_number = serializers.CharField(source="user.phone", read_only=True)

    class Meta:
        model = TeamMember
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "phone_number",
            "role_name",
            "role_id",
            "status",
            "joined_at",
            "last_login",
        ]


class TeamMemberReinviteSerializer(serializers.ModelSerializer):
    class Meta:
        model = TeamMember
        fields = []

    def validate(self, attrs):
        member = self.instance
        if member.status == TeamMemberStatus.Active.value:
            raise serializers.ValidationError("Cannot reinvite an active team member.")
        if member.status == TeamMemberStatus.Revoked.value:
            raise serializers.ValidationError("Cannot reinvite a revoked team member.")
        return attrs

    def save(self, **kwargs):
        member = self.instance
        user = member.user
        role = member.role

        token_str = generate_token(user)
        token, _ = Token.objects.update_or_create(
            user=user,
            token_type="CreateToken",
            defaults={
                "user": user,
                "token_type": "CreateToken",
                "token": token_str,
            },
        )

        email_data = {
            "first_name": user.firstname,
            "email": user.email,
            "url": f"{settings.CLIENT_URL}/verify-user/?token={token.token}",
            "token": token.token,
            "role_name": role.name,
        }
        send_merchant_teams_registration_email.delay(email_data)
        return member


class TeamMemberRevokeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TeamMember
        fields = []

    def validate(self, attrs):
        if self.instance.status != TeamMemberStatus.Invited.value:
            raise serializers.ValidationError("Only invited members can be revoked.")
        return attrs

    def save(self, **kwargs):
        instance: TeamMember = self.instance
        instance.revoke_invitation()
        return instance


class TeamMemberReactivateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TeamMember
        fields = []

    def validate(self, attrs):
        member = self.instance
        if member.status != TeamMemberStatus.Deactivated.value:
            raise serializers.ValidationError(
                "You can only reactivate a deactivated team member."
            )
        return attrs

    def save(self, **kwargs):
        member: TeamMember = self.instance
        member.reactivate()
        return member


class TeamMemberDeactivateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TeamMember
        fields = []

    def validate(self, attrs):
        member = self.instance
        if member.status != TeamMemberStatus.Active.value:
            raise serializers.ValidationError(
                "You can only deactivate an active team member."
            )
        return attrs

    def save(self, **kwargs):
        member: TeamMember = self.instance
        member.deactivate()
        return member


# ================================== ADMIN ===================================


class AdminTeamMemberInviteSerializer(BaseMemberInviteSerializer):
    role_id = serializers.PrimaryKeyRelatedField(
        queryset=Role.objects.filter(platform=SystemPlatform.Admin.value)
    )

    @transaction.atomic
    def create(self, validated_data):
        email = validated_data["email"]
        role = validated_data["role_id"]
        user = User.objects.create(
            email=email,
            firstname=validated_data["first_name"],
            lastname=validated_data["last_name"],
            phone=validated_data["phone_number"],
            password=make_password(None),
            role=SystemBaseUserRole.Admin.value,
        )

        admin_member = AdminProfile.objects.create(
            user=user,
            role=role,
            invited_by=self.context["request"].user,
            status=TeamMemberStatus.Invited.value,
        )

        token_str = generate_token(user)
        token, _ = Token.objects.update_or_create(
            user=user,
            token_type="CreateToken",
            defaults={
                "user": user,
                "token_type": "CreateToken",
                "token": token_str,
            },
        )

        email_data = {
            "first_name": user.firstname,
            "email": user.email,
            "url": f"{settings.CLIENT_URL}/verify-user/?token={token.token}",
            "token": token.token,
            "role_name": role.name,
        }
        send_merchant_teams_registration_email.delay(email_data)
        return admin_member


class AdminTeamMemberSerializer(serializers.ModelSerializer):
    role_name = serializers.CharField(source="role.name", read_only=True)
    last_login = serializers.DateTimeField(source="user.last_login", read_only=True)
    role_id = serializers.CharField(source="role.id", read_only=True)
    email = serializers.EmailField(source="user.email", read_only=True)
    first_name = serializers.CharField(source="user.firstname", read_only=True)
    last_name = serializers.CharField(source="user.lastname", read_only=True)
    phone_number = serializers.CharField(source="user.phone", read_only=True)

    class Meta:
        model = AdminProfile
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "phone_number",
            "role_name",
            "role_id",
            "status",
            "joined_at",
            "last_login",
        ]


class AdminTeamMemberReinviteSerializer(TeamMemberReinviteSerializer):
    class Meta:
        model = AdminProfile
        fields = []


class AdminTeamMemberRevokeSerializer(TeamMemberRevokeSerializer):
    class Meta:
        model = AdminProfile
        fields = []


class AdminTeamMemberReactivateSerializer(TeamMemberReactivateSerializer):
    class Meta:
        model = AdminProfile
        fields = []


class AdminTeamMemberDeactivateSerializer(TeamMemberDeactivateSerializer):
    class Meta:
        model = AdminProfile
        fields = []
