from django.urls import include, path
from rest_framework.routers import Default<PERSON>out<PERSON>
from teams.v1.views import (
    AdminTeamMemberViewSet,
    BusinessTeamMemberViewSet,
    RoleViewSet,
)

router = DefaultRouter()
router.register(r"roles", RoleViewSet, basename="role")
router.register(r"admin/members", AdminTeamMemberViewSet, basename="admin-team-member")
router.register(r"members", BusinessTeamMemberViewSet, basename="business-team-member")

urlpatterns = [
    path("", include(router.urls)),
]
