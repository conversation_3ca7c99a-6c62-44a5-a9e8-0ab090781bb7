from common.pagination import LargeDatasetKeySetPagination
from common.permissions import <PERSON><PERSON>d<PERSON>, IsBusinessOwner, IsPlatformOwner
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from teams.enums import (
    DefaultAdminRole,
    SystemBaseUserRole,
    SystemPlatform,
    SystemRoleType,
    TeamMemberStatus,
)
from teams.models import AdminProfile, Role, TeamMember
from teams.v1.serializers import (
    AdminTeamMemberDeactivateSerializer,
    AdminTeamMemberInviteSerializer,
    AdminTeamMemberReactivateSerializer,
    AdminTeamMemberReinviteSerializer,
    AdminTeamMemberRevokeSerializer,
    AdminTeamMemberSerializer,
    RoleSerializer,
    TeamMemberDeactivateSerializer,
    TeamMemberInviteSerializer,
    TeamMemberReactivateSerializer,
    TeamMemberReinviteSerializer,
    TeamMemberRevokeSerializer,
    TeamMemberSerializer,
)


class RoleViewSet(viewsets.ModelViewSet):
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated, IsPlatformOwner]
    http_method_names = ["get"]
    pagination_class = LargeDatasetKeySetPagination

    def get_queryset(self):
        user_role = self.request.user.role

        if user_role == SystemBaseUserRole.BusinessOwner.value:
            platform = SystemPlatform.Business.value
            queryset = Role.objects.filter(platform=platform).exclude(
                name=SystemBaseUserRole.BusinessOwner.value
            )
        else:
            platform = SystemPlatform.Admin.value
            queryset = Role.objects.filter(platform=platform).exclude(
                name__in=[
                    DefaultAdminRole.CustomerSupport.value,
                    DefaultAdminRole.Operations.value,
                    DefaultAdminRole.Reconciliation.value,
                    DefaultAdminRole.SuperAdmin.value,
                ]
            )
        return queryset.order_by("name")

    def perform_create(self, serializer):
        user = self.request.user
        if user.role == SystemBaseUserRole.BusinessOwner.value:
            platform = SystemPlatform.Business.value
            business = getattr(user, "business", None)
            serializer.save(
                platform=platform, business=business, type=SystemRoleType.Custom.value
            )
        else:
            platform = SystemPlatform.Admin.value
            serializer.save(platform=platform, type=SystemRoleType.Custom.value)

    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        serializer = self.get_serializer(self.get_object())
        serializer.delete()
        return Response({"message": "Role deleted"}, status=status.HTTP_200_OK)


class BusinessTeamMemberViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated, IsBusinessOwner]
    pagination_class = LargeDatasetKeySetPagination
    http_method_names = ["get", "post", "patch"]
    queryset = TeamMember.objects.select_related("user", "role", "invited_by")

    def get_queryset(self):
        return self.queryset.filter(business=self.request.user.business).exclude(
            status=TeamMemberStatus.Revoked.value
        )

    def get_serializer_class(self):
        if self.action == "create":
            return TeamMemberInviteSerializer
        if self.action == "reinvite":
            return TeamMemberReinviteSerializer
        if self.action == "revoke":
            return TeamMemberRevokeSerializer
        if self.action == "reactivate":
            return TeamMemberReactivateSerializer
        if self.action == "deactivate":
            return TeamMemberDeactivateSerializer
        return TeamMemberSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context.update(
            {
                "platform": SystemPlatform.Business.value,
                "business": self.request.user.business,
            }
        )
        return context

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        team_member = serializer.save()
        response_serializer = TeamMemberSerializer(
            team_member, context=self.get_serializer_context()
        )
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def reinvite(self, request, pk=None):
        instance = self.get_object()
        serializer = self.get_serializer(instance=instance, data={})
        serializer.is_valid(raise_exception=True)
        team_member = serializer.save()
        response_serializer = TeamMemberSerializer(
            team_member, context=self.get_serializer_context()
        )
        return Response(
            {
                "status": True,
                "message": "Invitation resent.",
                "data": response_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def revoke(self, request, pk=None):
        instance = self.get_object()
        serializer = self.get_serializer(instance=instance, data={})
        serializer.is_valid(raise_exception=True)
        team_member = serializer.save()
        response_serializer = TeamMemberSerializer(
            team_member, context=self.get_serializer_context()
        )
        return Response(
            {
                "status": True,
                "message": "Team member access revoked successfully.",
                "data": response_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def reactivate(self, request, pk=None):
        instance = self.get_object()
        serializer = self.get_serializer(instance=instance, data={})
        serializer.is_valid(raise_exception=True)
        team_member = serializer.save()
        response_serializer = TeamMemberSerializer(
            team_member, context=self.get_serializer_context()
        )
        return Response(
            {
                "status": True,
                "message": "Team member reactivated successfully.",
                "data": response_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def deactivate(self, request, pk=None):
        instance = self.get_object()
        serializer = self.get_serializer(instance=instance, data={})
        serializer.is_valid(raise_exception=True)
        team_member = serializer.save()
        response_serializer = TeamMemberSerializer(
            team_member, context=self.get_serializer_context()
        )
        return Response(
            {
                "status": True,
                "message": "Team member deactivated successfully.",
                "data": response_serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class AdminTeamMemberViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAdmin]
    pagination_class = LargeDatasetKeySetPagination
    http_method_names = ["get", "post", "patch"]
    queryset = AdminProfile.objects.select_related("user", "role", "invited_by")

    def get_queryset(self):
        return self.queryset.all().exclude(status=TeamMemberStatus.Revoked.value)

    def get_serializer_class(self):
        if self.action == "create":
            return AdminTeamMemberInviteSerializer
        if self.action == "reinvite":
            return AdminTeamMemberReinviteSerializer
        if self.action == "revoke":
            return AdminTeamMemberRevokeSerializer
        if self.action == "reactivate":
            return AdminTeamMemberReactivateSerializer
        if self.action == "deactivate":
            return AdminTeamMemberDeactivateSerializer
        return AdminTeamMemberSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context.update(
            {
                "platform": SystemPlatform.Admin.value,
            }
        )
        return context

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        team_member = serializer.save()
        response_serializer = AdminTeamMemberSerializer(
            team_member, context=self.get_serializer_context()
        )
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def reinvite(self, request, pk=None):
        instance = self.get_object()
        serializer = self.get_serializer(instance=instance, data={})
        serializer.is_valid(raise_exception=True)
        team_member = serializer.save()
        response_serializer = AdminTeamMemberSerializer(
            team_member, context=self.get_serializer_context()
        )
        return Response(
            {
                "status": True,
                "message": "Invitation resent.",
                "data": response_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def revoke(self, request, pk=None):
        instance = self.get_object()
        serializer = self.get_serializer(instance=instance, data={})
        serializer.is_valid(raise_exception=True)
        team_member = serializer.save()
        response_serializer = AdminTeamMemberSerializer(
            team_member, context=self.get_serializer_context()
        )
        return Response(
            {
                "status": True,
                "message": "Admin access revoked successfully.",
                "data": response_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def reactivate(self, request, pk=None):
        instance = self.get_object()
        serializer = self.get_serializer(instance=instance, data={})
        serializer.is_valid(raise_exception=True)
        team_member = serializer.save()
        response_serializer = AdminTeamMemberSerializer(
            team_member, context=self.get_serializer_context()
        )
        return Response(
            {
                "status": True,
                "message": "Admin reactivated successfully.",
                "data": response_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def deactivate(self, request, pk=None):
        instance = self.get_object()
        serializer = self.get_serializer(instance=instance, data={})
        serializer.is_valid(raise_exception=True)
        team_member = serializer.save()
        response_serializer = AdminTeamMemberSerializer(
            team_member, context=self.get_serializer_context()
        )
        return Response(
            {
                "status": True,
                "message": "Admin deactivated successfully.",
                "data": response_serializer.data,
            },
            status=status.HTTP_200_OK,
        )
