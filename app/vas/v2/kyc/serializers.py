import logging
from decimal import Decimal

from business.models import Business
from common.enums import ProductEnum, ServiceEnum, VasGateStatus
from common.responses import FailureResponse, PendingResponse, SuccessResponse
from config.utils import get_provider
from django.db import transaction
from fees.handlers.fee_processor_handler import FeeProcessorHandler
from ledger.enums import LedgerTypeEnum
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum, TransactionStatusEnum
from transaction.handlers.base import TransactionHandler
from transaction.tasks import handle_service_revenue
from vas.integrations.kyc import KYCVASGateClient

logger = logging.getLogger(__name__)


def create_base_txn_params(wallet, business, payload, merchant_reference=None):
    amount = payload.get("amount")
    service = payload.get("type")
    return CreateBaseTransactionParams(
        wallet=wallet,
        business=business,
        amount=Decimal(amount),
        charge=Decimal(0),
        txn_class=TransactionClassEnum.KYC.value,
        type=service,
        narration=f"{service} verification for {payload.get(service.lower())}",
        merchant_reference=merchant_reference,
        provider=payload.get("provider"),
    )


class VerifyBVNSerializer(serializers.Serializer):
    bvn = serializers.CharField(max_length=11, min_length=11)
    phone = serializers.CharField(required=False)
    reference = serializers.CharField(required=False)

    def save(self, **kwargs):
        business: Business = self.context["business"]
        wallet = business.get_general_wallet()

        payload = self.validated_data

        provider = get_provider(ProductEnum.Kyc, ServiceEnum.Bvn)
        payload["provider"] = provider.value
        payload["type"] = ServiceEnum.Bvn.value

        payload["amount"] = FeeProcessorHandler(
            Decimal(0), ProductEnum.Kyc.value, ServiceEnum.Bvn.value, business
        ).get_business_fee()

        handler = TransactionHandler()

        params = create_base_txn_params(
            wallet, business, payload, payload.get("reference")
        )
        txn = handler.debit_wallet(params)
        payload["reference"] = txn.reference

        service = payload["type"]
        vas_extra_fields = {"identity": payload.get("bvn"), "type": service.upper()}
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.KYC.value,
            extra_fields=vas_extra_fields,
        )

        _, response = KYCVASGateClient().verify_bvn(payload)
        response_status = response.get("status")

        if response_status == VasGateStatus.Success.value:

            with transaction.atomic():
                txn.status = TransactionStatusEnum.SUCCESSFUL.value
                txn.save()

                personal_info = response["data"]["personal_info"]

                handler.update_vas_transaction(
                    txn,
                    vas_txn,
                    {
                        "status": txn.status,
                        "full_name": personal_info["full_name"],
                        "phone": personal_info["phone_number"],
                        "gender": personal_info["gender"],
                        "dob": personal_info["date_of_birth"],
                    },
                )

                handle_service_revenue.delay_on_commit(txn.id, business.id)

            return SuccessResponse(
                message="Bvn verification successful.",
                data=response.get("data", None),
                top_level_data={"reference": txn.merchant_reference},
            )

        if response_status == VasGateStatus.Failed.value:
            handler.reverse_transaction(txn, vas_txn, wallet, LedgerTypeEnum.KYC)

            return FailureResponse(
                message=response.get("message", None) or "Bvn verification failed.",
                data=response.get("data", None),
                top_level_data={"reference": txn.merchant_reference},
                status_code=400,
            )

        return PendingResponse(
            message="Bvn verification pending.",
            data=response.get("data", None),
            top_level_data={"reference": txn.merchant_reference},
        )


class VerifyNINSerializer(serializers.Serializer):
    nin = serializers.CharField()
    phone = serializers.CharField(required=False)

    def save(self, **kwargs):
        business: Business = self.context["business"]
        wallet = business.get_general_wallet()

        payload = self.validated_data

        provider = get_provider(ProductEnum.Kyc, ServiceEnum.Nin)
        payload["provider"] = provider.value
        payload["type"] = ServiceEnum.Nin.value

        payload["amount"] = FeeProcessorHandler(
            Decimal(0), ProductEnum.Kyc.value, ServiceEnum.Nin.value, business
        ).get_business_fee()

        handler = TransactionHandler()

        params = create_base_txn_params(
            wallet, business, payload, payload.get("reference")
        )
        txn = handler.debit_wallet(params)
        payload["reference"] = txn.reference

        service = payload["type"]
        vas_extra_fields = {"identity": payload.get("nin"), "type": service.upper()}
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.KYC.value,
            extra_fields=vas_extra_fields,
        )

        _, response = KYCVASGateClient().verify_nin(payload)
        response_status = response.get("status")

        if response_status == VasGateStatus.Success.value:

            with transaction.atomic():
                txn.status = TransactionStatusEnum.SUCCESSFUL.value
                txn.save()

                personal_info = response["data"]["personal_info"]

                handler.update_vas_transaction(
                    txn,
                    vas_txn,
                    {
                        "status": txn.status,
                        "full_name": personal_info["full_name"],
                        "phone": personal_info["phone_number"],
                        "gender": personal_info["gender"],
                        "dob": personal_info["date_of_birth"],
                    },
                )

                handle_service_revenue.delay_on_commit(txn.id, business.id)

            return SuccessResponse(
                message="Nin verification successful.",
                data=response.get("data", None),
                top_level_data={"reference": txn.merchant_reference},
            )

        if response_status == VasGateStatus.Failed.value:

            handler.reverse_transaction(txn, vas_txn, wallet, LedgerTypeEnum.KYC)

            return FailureResponse(
                message=response.get("message", None) or "Nin verification failed.",
                data=response.get("data", None),
                top_level_data={"reference": txn.merchant_reference},
                status_code=400,
            )

        return PendingResponse(
            message="Nin verification pending.",
            data=response.get("data", None),
            top_level_data={"reference": txn.merchant_reference},
        )


class PhoneNumberLookupSerializer(serializers.Serializer):
    phone = serializers.CharField()
    reference = serializers.CharField()

    def save(self, **kwargs):
        business: Business = self.context["business"]
        wallet = business.get_general_wallet()

        payload = self.validated_data

        provider = get_provider(ProductEnum.Kyc, ServiceEnum.PhoneNumberLookup)
        payload["provider"] = provider.value
        payload["type"] = ServiceEnum.PhoneNumberLookup.value

        payload["amount"] = FeeProcessorHandler(
            Decimal(0),
            ProductEnum.Kyc.value,
            ServiceEnum.PhoneNumberLookup.value,
            business,
        ).get_business_fee()

        handler = TransactionHandler()

        params = create_base_txn_params(
            wallet, business, payload, payload.get("reference")
        )
        txn = handler.debit_wallet(params)
        payload["reference"] = txn.reference

        service = payload["type"]
        vas_extra_fields = {"identity": payload.get("nin"), "type": service.upper()}
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.KYC.value,
            extra_fields=vas_extra_fields,
        )

        _, response = KYCVASGateClient().lookup_phone_number(payload)
        response_status = response.get("status")

        if response_status == VasGateStatus.Success.value:
            with transaction.atomic():
                txn.status = TransactionStatusEnum.SUCCESSFUL.value
                txn.save()

                personal_info = response["data"]["personal_info"]

                handler.update_vas_transaction(
                    txn,
                    vas_txn,
                    {
                        "status": txn.status,
                        "full_name": personal_info["full_name"],
                        "phone": personal_info["phone_number"],
                        "gender": personal_info["gender"],
                        "dob": personal_info["date_of_birth"],
                    },
                )

                handle_service_revenue.delay_on_commit(txn.id, business.id)

            return SuccessResponse(
                message="Nin verification successful.",
                data=response.get("data", None),
                top_level_data={"reference": txn.merchant_reference},
            )

        if response_status == VasGateStatus.Failed.value:
            handler.reverse_transaction(txn, vas_txn, wallet, LedgerTypeEnum.KYC)

            return FailureResponse(
                message=response.get("message", None) or "Nin verification failed.",
                data=response.get("data", None),
                top_level_data={"reference": txn.merchant_reference},
                status_code=400,
            )

        return PendingResponse(
            message="Nin verification pending.",
            data=response.get("data", None),
            top_level_data={"reference": txn.merchant_reference},
        )
