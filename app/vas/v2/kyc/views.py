from business.models import Business
from common.decorators import (
    enforce_unique_reference,
    require_activated_vas_product,
)
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import generics
from rest_framework.response import Response
from transaction.enums import TransactionClassEnum

from ..auth.authentication import BusinessJWTAuthentication
from .serializers import (
    PhoneNumberLookupSerializer,
    VerifyBVNSerializer,
    VerifyNINSerializer,
)


@extend_schema_view(
    post=extend_schema(
        summary="Verify BVN",
        tags=["vas-kyc"],
    )
)
@require_activated_vas_product(TransactionClassEnum.KYC.value)
class VerifyBVNView(generics.GenericAPIView):
    serializer_class = VerifyBVNSerializer
    authentication_classes = [BusinessJWTAuthentication]

    @enforce_unique_reference
    def post(self, request, *args, **kwargs):
        business: Business = request.user
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data.to_dict(), status=data.status_code)


@extend_schema_view(
    post=extend_schema(
        summary="Verify NIN",
        tags=["vas-kyc"],
    )
)
@require_activated_vas_product(TransactionClassEnum.KYC.value)
class VerifyNINView(generics.GenericAPIView):
    serializer_class = VerifyNINSerializer
    authentication_classes = [BusinessJWTAuthentication]

    @enforce_unique_reference
    def post(self, request, *args, **kwargs):
        business: Business = request.user
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data.to_dict(), status=data.status_code)


@extend_schema_view(
    post=extend_schema(
        summary="Phone Number Lookup",
        tags=["vas-kyc"],
    )
)
@require_activated_vas_product(TransactionClassEnum.KYC.value)
class PhoneNumberLookupView(generics.GenericAPIView):
    serializer_class = PhoneNumberLookupSerializer
    authentication_classes = [BusinessJWTAuthentication]

    @enforce_unique_reference
    def post(self, request, *args, **kwargs):
        business: Business = request.user
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data.to_dict(), status=data.status_code)


@extend_schema_view(
    post=extend_schema(
        summary="Verify BVN with Image",
        tags=["vas-kyc"],
    )
)
@require_activated_vas_product(TransactionClassEnum.KYC.value)
class VerifyBVNWithImageView(generics.GenericAPIView):
    serializer_class = VerifyBVNSerializer
    authentication_classes = [BusinessJWTAuthentication]

    @enforce_unique_reference
    def post(self, request, *args, **kwargs):
        business: Business = request.user
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data.to_dict(), status=data.status_code)
