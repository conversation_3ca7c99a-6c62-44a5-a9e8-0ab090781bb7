import logging
from decimal import Decimal

from business.models import Business
from common.enums import AirtimeNetworkEnum, ProductEnum, ServiceEnum, VasGateStatus
from common.responses import FailureResponse, PendingResponse, SuccessResponse
from config.utils import get_provider
from django.db import transaction
from ledger.enums import LedgerTypeEnum
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum, TransactionStatusEnum
from transaction.handlers.base import TransactionHandler
from transaction.models.airtime import AirtimeVASTransaction
from transaction.tasks import process_commission
from vas.integrations.airtime import AirtimeVASGateClient

logger = logging.getLogger(__name__)


class AirtimePurchaseRequestSerializer(serializers.Serializer):
    network = serializers.ChoiceField(choices=AirtimeNetworkEnum.choices())
    reference = serializers.CharField()
    phone = serializers.CharField()
    amount = serializers.IntegerField()

    def save(self, **kwargs):
        business: Business = self.context["business"]
        wallet = business.get_general_wallet()

        payload = self.validated_data
        network = payload.get("network")
        reference = payload.get("reference")
        phone_number = payload.get("phone")

        provider = get_provider(ProductEnum.Airtime, ServiceEnum(network.capitalize()))
        payload["provider"] = provider.value

        handler = TransactionHandler()

        params = self.__create_base_txn_params(wallet, business, reference, payload)
        txn = handler.debit_wallet(params)

        vas_extra_fields = {"network": network, "phone_number": phone_number}
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.AIRTIME.value,
            extra_fields=vas_extra_fields,
        )

        payload["reference"] = txn.reference
        _, response = AirtimeVASGateClient().purchase_airtime(payload)
        response_status = response.get("status")

        if response_status == VasGateStatus.Success.value:

            with transaction.atomic():
                txn.status = TransactionStatusEnum.SUCCESSFUL.value
                txn.save()

                handler.update_vas_transaction(txn, vas_txn, {"status": txn.status})

                process_commission.delay_on_commit(txn.id, business.id)

            return SuccessResponse(
                message="Airtime purchased successfully.",
                data=response.get("data", None),
                top_level_data={"reference": txn.merchant_reference},
            )

        if response_status == VasGateStatus.Failed.value:

            handler.reverse_transaction(txn, vas_txn, wallet, LedgerTypeEnum.AIRTIME)

            return FailureResponse(
                message=response.get("message", None) or "Airtime purchased failed.",
                data=response.get("data", None),
                top_level_data={"reference": txn.merchant_reference},
                status_code=400,
            )

        return PendingResponse(
            message="Airtime purchase pending.",
            data=response.get("data", None),
            top_level_data={"reference": txn.merchant_reference},
        )

    @staticmethod
    def __create_base_txn_params(wallet, business, reference, payload):
        amount = payload.get("amount")
        network = payload.get("network")
        phone = payload.get("phone")
        return CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=Decimal(amount),
            charge=Decimal(0),
            txn_class=TransactionClassEnum.AIRTIME.value,
            type=network,
            narration=f"N{amount} {network} Airtime Purchase on {phone}",
            merchant_reference=reference,
            provider=payload.get("provider"),
        )


class AirtimeVASTransactionSerializer(serializers.ModelSerializer):

    class Meta:
        model = AirtimeVASTransaction
        fields = "__all__"
