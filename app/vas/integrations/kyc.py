from vas.integrations.base import BaseVASGateClient


class KYCVASGateClient(BaseVASGateClient):
    def __init__(self):
        super().__init__()

    def verify_bvn(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/kyc/verify-bvn/",
            data={
                "bvn": payload.get("bvn"),
                "phone": payload.get("phone"),
                "reference": payload.get("reference"),
                "provider": payload.get("provider"),
            },
        )
        return self._map_response(response, payload.get("reference"), status_code)

    def verify_nin(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/kyc/verify-nin/",
            data={
                "nin": payload.get("nin"),
                "phone": payload.get("phone"),
                "reference": payload.get("reference"),
                "provider": payload.get("provider"),
            },
        )
        return self._map_response(response, payload.get("reference"), status_code)

    def lookup_phone_number(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/kyc/phone-number-lookup/",
            data={
                "phone": payload.get("phone"),
                "reference": payload.get("reference"),
                "provider": payload.get("provider"),
            },
        )
        return self._map_response(response, payload.get("reference"), status_code)
