from vas.integrations.base import BaseVASGateClient


class WalletBalanceGateClient(BaseVASGateClient):
    def __init__(self):
        super().__init__()

    def fetch_wallet_balance(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/wallet-balance/check-balance/",
            data=payload,
        )
        return str(response)
