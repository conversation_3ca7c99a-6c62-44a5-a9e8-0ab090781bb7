from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .views import ExportViewSet
from .action_views import (
    AllTransactionsExportView,
    VirtualAccountTransactionsExportView,
    AirtimeTransactionsExportView,
    DataTransactionsExportView,
    ElectricityTransactionsExportView,
    CableTVTransactionsExportView,
    BettingTransactionsExportView,
    EducationTransactionsExportView,
    KYCTransactionsExportView,
    EpinTransactionsExportView,
    CommissionsExportView,
    DisputesExportView,
    AuditLogsExportView,
    ExportFieldsView,
    ExportTypesView,
)

router = DefaultRouter()
router.register(r"exports", ExportViewSet, basename="export")

urlpatterns = [
    path("", include(router.urls)),

    # Per-action export endpoints
    path("exports/actions/all-transactions/", AllTransactionsExportView.as_view(), name="export-all-transactions"),
    path("exports/actions/virtual-account-transactions/", VirtualAccountTransactionsExportView.as_view(), name="export-virtual-account-transactions"),
    path("exports/actions/airtime-transactions/", AirtimeTransactionsExportView.as_view(), name="export-airtime-transactions"),
    path("exports/actions/data-transactions/", DataTransactionsExportView.as_view(), name="export-data-transactions"),
    path("exports/actions/electricity-transactions/", ElectricityTransactionsExportView.as_view(), name="export-electricity-transactions"),
    path("exports/actions/cable-tv-transactions/", CableTVTransactionsExportView.as_view(), name="export-cable-tv-transactions"),
    path("exports/actions/betting-transactions/", BettingTransactionsExportView.as_view(), name="export-betting-transactions"),
    path("exports/actions/education-transactions/", EducationTransactionsExportView.as_view(), name="export-education-transactions"),
    path("exports/actions/kyc-transactions/", KYCTransactionsExportView.as_view(), name="export-kyc-transactions"),
    path("exports/actions/epin-transactions/", EpinTransactionsExportView.as_view(), name="export-epin-transactions"),
    path("exports/actions/commissions/", CommissionsExportView.as_view(), name="export-commissions"),
    path("exports/actions/disputes/", DisputesExportView.as_view(), name="export-disputes"),
    path("exports/actions/audit-logs/", AuditLogsExportView.as_view(), name="export-audit-logs"),

    # Helper endpoints
    path("exports/fields/", ExportFieldsView.as_view(), name="export-fields"),
    path("exports/fields/<str:export_type>/", ExportFieldsView.as_view(), name="export-fields-by-type"),
    path("exports/types/", ExportTypesView.as_view(), name="export-types"),
]
