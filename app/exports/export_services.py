"""
Specific export services for different data types
"""
import logging
from typing import Dict, Any, List, Optional
from django.apps import apps
from django.db.models import QuerySet
from django.utils import timezone
from datetime import datetime, timedelta

from .models import ExportRequest
from .services import ExportService

logger = logging.getLogger(__name__)


class BaseExportService:
    """Base class for specific export services"""
    
    export_type = None
    model_name = None
    default_fields = []
    
    def __init__(self):
        self.export_service = ExportService()
    
    def get_model_class(self):
        """Get the Django model class"""
        return apps.get_model(self.model_name)
    
    def get_default_fields(self) -> List[str]:
        """Get default fields for this export type"""
        return self.default_fields.copy()
    
    def apply_business_filter(self, queryset: QuerySet, business) -> QuerySet:
        """Apply business-specific filtering"""
        return queryset.filter(business=business)
    
    def apply_date_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply date range filters"""
        if filters.get('date_from'):
            try:
                date_from = datetime.fromisoformat(filters['date_from'].replace('Z', '+00:00'))
                queryset = queryset.filter(created_at__gte=date_from)
            except (ValueError, TypeError):
                pass
        
        if filters.get('date_to'):
            try:
                date_to = datetime.fromisoformat(filters['date_to'].replace('Z', '+00:00'))
                queryset = queryset.filter(created_at__lte=date_to)
            except (ValueError, TypeError):
                pass
        
        return queryset
    
    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply custom filters specific to this export type"""
        return queryset
    
    def build_queryset(self, business, filters: Dict[str, Any]) -> QuerySet:
        """Build the complete queryset for export"""
        model_class = self.get_model_class()
        queryset = model_class.objects.all()
        
        # Apply business filter
        queryset = self.apply_business_filter(queryset, business)
        
        # Apply date filters
        queryset = self.apply_date_filters(queryset, filters)
        
        # Apply custom filters
        queryset = self.apply_custom_filters(queryset, filters)
        
        return queryset.order_by('-created_at')
    
    def create_export_request(self, user, business, fields_to_export: List[str] = None, 
                            filters: Dict[str, Any] = None) -> ExportRequest:
        """Create export request for this specific export type"""
        if not fields_to_export:
            fields_to_export = self.get_default_fields()
        
        return self.export_service.create_export_request(
            user=user,
            business=business,
            model_name=self.model_name,
            export_type=self.export_type,
            fields_to_export=fields_to_export,
            filters=filters or {}
        )


class AllTransactionsExportService(BaseExportService):
    """Export service for all transactions"""
    
    export_type = ExportRequest.ExportType.ALL_TRANSACTIONS
    model_name = "transaction.Transaction"
    default_fields = [
        'id', 'reference', 'merchant_reference', 'status', 'mode', 'txn_class',
        'type', 'amount', 'charge', 'revenue', 'net_amount', 'old_balance',
        'new_balance', 'narration', 'provider', 'created_at'
    ]
    
    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply transaction-specific filters"""
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])
        
        if filters.get('txn_class'):
            queryset = queryset.filter(txn_class=filters['txn_class'])
        
        if filters.get('provider'):
            queryset = queryset.filter(provider=filters['provider'])
        
        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass
        
        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass
        
        return queryset


class VirtualAccountTransactionsExportService(BaseExportService):
    """Export service for virtual account transactions"""
    
    export_type = ExportRequest.ExportType.VIRTUAL_ACCOUNT_TRANSACTIONS
    model_name = "transaction.VirtualAccountVasTransaction"
    default_fields = [
        'id', 'reference', 'merchant_reference', 'status', 'mode', 'amount',
        'charge', 'net_amount', 'session_id', 'source_account_number',
        'source_account_name', 'source_bank_name', 'recipient_account_number',
        'recipient_account_name', 'recipient_bank_name', 'created_at'
    ]
    
    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply virtual account transaction-specific filters"""
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])
        
        if filters.get('source_bank_name'):
            queryset = queryset.filter(source_bank_name__icontains=filters['source_bank_name'])
        
        if filters.get('recipient_bank_name'):
            queryset = queryset.filter(recipient_bank_name__icontains=filters['recipient_bank_name'])
        
        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass
        
        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass
        
        return queryset


class AirtimeTransactionsExportService(BaseExportService):
    """Export service for airtime transactions"""
    
    export_type = ExportRequest.ExportType.AIRTIME_TRANSACTIONS
    model_name = "transaction.AirtimeVASTransaction"
    default_fields = [
        'id', 'reference', 'merchant_reference', 'status', 'mode', 'amount',
        'charge', 'net_amount', 'phone_number', 'network', 'created_at'
    ]
    
    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply airtime transaction-specific filters"""
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])
        
        if filters.get('network'):
            queryset = queryset.filter(network=filters['network'])
        
        if filters.get('phone_number'):
            queryset = queryset.filter(phone_number__icontains=filters['phone_number'])
        
        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass
        
        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass
        
        return queryset


class ElectricityTransactionsExportService(BaseExportService):
    """Export service for electricity transactions"""
    
    export_type = ExportRequest.ExportType.ELECTRICITY_TRANSACTIONS
    model_name = "transaction.ElectricityVASTransaction"
    default_fields = [
        'id', 'reference', 'merchant_reference', 'status', 'mode', 'amount',
        'charge', 'net_amount', 'meter_number', 'disco', 'meter_type',
        'customer_name', 'customer_address', 'created_at'
    ]
    
    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply electricity transaction-specific filters"""
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])
        
        if filters.get('disco'):
            queryset = queryset.filter(disco=filters['disco'])
        
        if filters.get('meter_type'):
            queryset = queryset.filter(meter_type=filters['meter_type'])
        
        if filters.get('meter_number'):
            queryset = queryset.filter(meter_number__icontains=filters['meter_number'])
        
        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass
        
        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass
        
        return queryset


class KYCTransactionsExportService(BaseExportService):
    """Export service for KYC transactions"""
    
    export_type = ExportRequest.ExportType.KYC_TRANSACTIONS
    model_name = "transaction.KYCVASTransaction"
    default_fields = [
        'id', 'reference', 'merchant_reference', 'status', 'mode', 'amount',
        'charge', 'net_amount', 'narration', 'created_at'
    ]
    
    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply KYC transaction-specific filters"""
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])
        
        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass
        
        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass
        
        return queryset


class DataTransactionsExportService(BaseExportService):
    """Export service for data transactions"""

    export_type = ExportRequest.ExportType.DATA_TRANSACTIONS
    model_name = "transaction.DataVASTransaction"
    default_fields = [
        'id', 'reference', 'merchant_reference', 'status', 'mode', 'amount',
        'charge', 'net_amount', 'phone_number', 'network', 'data_plan',
        'created_at'
    ]

    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply data transaction-specific filters"""
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])

        if filters.get('network'):
            queryset = queryset.filter(network=filters['network'])

        if filters.get('phone_number'):
            queryset = queryset.filter(phone_number__icontains=filters['phone_number'])

        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass

        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass

        return queryset


class CableTVTransactionsExportService(BaseExportService):
    """Export service for cable TV transactions"""

    export_type = ExportRequest.ExportType.CABLE_TV_TRANSACTIONS
    model_name = "transaction.CableTVVASTransaction"
    default_fields = [
        'id', 'reference', 'merchant_reference', 'status', 'mode', 'amount',
        'charge', 'net_amount', 'smartcard_number', 'cable_tv_provider',
        'package_name', 'customer_name', 'created_at'
    ]

    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply cable TV transaction-specific filters"""
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])

        if filters.get('cable_tv_provider'):
            queryset = queryset.filter(cable_tv_provider=filters['cable_tv_provider'])

        if filters.get('smartcard_number'):
            queryset = queryset.filter(smartcard_number__icontains=filters['smartcard_number'])

        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass

        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass

        return queryset


class BettingTransactionsExportService(BaseExportService):
    """Export service for betting transactions"""

    export_type = ExportRequest.ExportType.BETTING_TRANSACTIONS
    model_name = "transaction.BettingVASTransaction"
    default_fields = [
        'id', 'reference', 'merchant_reference', 'status', 'mode', 'amount',
        'charge', 'net_amount', 'customer_id', 'betting_provider',
        'customer_name', 'created_at'
    ]

    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply betting transaction-specific filters"""
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])

        if filters.get('betting_provider'):
            queryset = queryset.filter(betting_provider=filters['betting_provider'])

        if filters.get('customer_id'):
            queryset = queryset.filter(customer_id__icontains=filters['customer_id'])

        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass

        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass

        return queryset


class EducationTransactionsExportService(BaseExportService):
    """Export service for education transactions"""

    export_type = ExportRequest.ExportType.EDUCATION_TRANSACTIONS
    model_name = "transaction.EducationVASTransaction"
    default_fields = [
        'id', 'reference', 'merchant_reference', 'status', 'mode', 'amount',
        'charge', 'net_amount', 'narration', 'created_at'
    ]

    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply education transaction-specific filters"""
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])

        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass

        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass

        return queryset


class EpinTransactionsExportService(BaseExportService):
    """Export service for epin transactions"""

    export_type = ExportRequest.ExportType.EPIN_TRANSACTIONS
    model_name = "transaction.EpinVASTransaction"
    default_fields = [
        'id', 'reference', 'merchant_reference', 'status', 'mode', 'amount',
        'charge', 'net_amount', 'narration', 'created_at'
    ]

    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply epin transaction-specific filters"""
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])

        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass

        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass

        return queryset


class CommissionsExportService(BaseExportService):
    """Export service for commission transactions"""

    export_type = ExportRequest.ExportType.COMMISSIONS
    model_name = "transaction.CommissionTransaction"
    default_fields = [
        'id', 'reference', 'source_transaction_reference', 'txn_class',
        'amount', 'narration', 'created_at'
    ]

    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply commission-specific filters"""
        if filters.get('txn_class'):
            queryset = queryset.filter(txn_class=filters['txn_class'])

        if filters.get('source_transaction_reference'):
            queryset = queryset.filter(
                source_transaction_reference__icontains=filters['source_transaction_reference']
            )

        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass

        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass

        return queryset


class DisputesExportService(BaseExportService):
    """Export service for disputes"""

    export_type = ExportRequest.ExportType.DISPUTES
    model_name = "dispute.Dispute"
    default_fields = [
        'id', 'dispute_type', 'topic', 'transaction_reference', 'vas_service',
        'amount', 'charge', 'status', 'message', 'created_at', 'resolved_at'
    ]

    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply dispute-specific filters"""
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])

        if filters.get('dispute_type'):
            queryset = queryset.filter(dispute_type=filters['dispute_type'])

        if filters.get('vas_service'):
            queryset = queryset.filter(vas_service=filters['vas_service'])

        if filters.get('transaction_reference'):
            queryset = queryset.filter(
                transaction_reference__icontains=filters['transaction_reference']
            )

        if filters.get('amount_min'):
            try:
                amount_min = float(filters['amount_min'])
                queryset = queryset.filter(amount__gte=amount_min)
            except (ValueError, TypeError):
                pass

        if filters.get('amount_max'):
            try:
                amount_max = float(filters['amount_max'])
                queryset = queryset.filter(amount__lte=amount_max)
            except (ValueError, TypeError):
                pass

        return queryset


class AuditLogsExportService(BaseExportService):
    """Export service for audit logs"""

    export_type = ExportRequest.ExportType.AUDIT_LOGS
    model_name = "audit.AuditLog"
    default_fields = [
        'id', 'email', 'action', 'description', 'ip_address', 'user_agent',
        'status', 'resource_type', 'resource_id', 'created_at'
    ]

    def apply_business_filter(self, queryset: QuerySet, business) -> QuerySet:
        """Apply business-specific filtering for audit logs"""
        # For audit logs, filter by business users
        business_user_emails = business.users.values_list('email', flat=True)
        return queryset.filter(email__in=business_user_emails)

    def apply_custom_filters(self, queryset: QuerySet, filters: Dict[str, Any]) -> QuerySet:
        """Apply audit log-specific filters"""
        if filters.get('action'):
            queryset = queryset.filter(action=filters['action'])

        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])

        if filters.get('resource_type'):
            queryset = queryset.filter(resource_type=filters['resource_type'])

        if filters.get('email'):
            queryset = queryset.filter(email__icontains=filters['email'])

        if filters.get('ip_address'):
            queryset = queryset.filter(ip_address__icontains=filters['ip_address'])

        return queryset


# Export service registry
EXPORT_SERVICES = {
    ExportRequest.ExportType.ALL_TRANSACTIONS: AllTransactionsExportService,
    ExportRequest.ExportType.VIRTUAL_ACCOUNT_TRANSACTIONS: VirtualAccountTransactionsExportService,
    ExportRequest.ExportType.AIRTIME_TRANSACTIONS: AirtimeTransactionsExportService,
    ExportRequest.ExportType.DATA_TRANSACTIONS: DataTransactionsExportService,
    ExportRequest.ExportType.ELECTRICITY_TRANSACTIONS: ElectricityTransactionsExportService,
    ExportRequest.ExportType.CABLE_TV_TRANSACTIONS: CableTVTransactionsExportService,
    ExportRequest.ExportType.BETTING_TRANSACTIONS: BettingTransactionsExportService,
    ExportRequest.ExportType.EDUCATION_TRANSACTIONS: EducationTransactionsExportService,
    ExportRequest.ExportType.KYC_TRANSACTIONS: KYCTransactionsExportService,
    ExportRequest.ExportType.EPIN_TRANSACTIONS: EpinTransactionsExportService,
    ExportRequest.ExportType.COMMISSIONS: CommissionsExportService,
    ExportRequest.ExportType.DISPUTES: DisputesExportService,
    ExportRequest.ExportType.AUDIT_LOGS: AuditLogsExportService,
}


def get_export_service(export_type: str) -> BaseExportService:
    """Get the appropriate export service for the given export type"""
    service_class = EXPORT_SERVICES.get(export_type)
    if not service_class:
        raise ValueError(f"Unsupported export type: {export_type}")
    return service_class()
