"""
Per-action export views for specific export types
"""
import logging
from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend

from .models import ExportRequest
from .serializers import ExportRequestSerializer, PerActionExportRequestSerializer
from .export_services import get_export_service
from .tasks import process_export_task
from .utils import log_export_activity

logger = logging.getLogger(__name__)


class BaseExportView(APIView):
    """Base view for export actions"""
    
    permission_classes = [permissions.IsAuthenticated]
    export_type = None
    
    def post(self, request, *args, **kwargs):
        """Create export request for specific export type"""
        user = request.user

        # Check if user has business
        if not hasattr(user, 'business') or not user.business:
            return Response(
                {"error": "User must be associated with a business"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate request data
        serializer = PerActionExportRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get export service for this type
            export_service = get_export_service(self.export_type)

            # Get validated data
            fields_to_export = serializer.validated_data.get('fields_to_export', [])
            filters = serializer.validated_data.get('filters', {})

            # Create export request
            export_request = export_service.create_export_request(
                user=user,
                business=user.business,
                fields_to_export=fields_to_export,
                filters=filters
            )

            # Start background processing
            process_export_task.delay(export_request.id)

            # Log export activity
            log_export_activity(
                user=user,
                action="CREATED",
                export_request_id=export_request.id,
                details=f"Created {self.export_type} export request"
            )

            # Return created export request
            response_serializer = ExportRequestSerializer(export_request)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except ValueError as e:
            logger.warning(f"Validation error creating {self.export_type} export: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error creating {self.export_type} export: {str(e)}")
            return Response(
                {"error": "Failed to create export request"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AllTransactionsExportView(BaseExportView):
    """Export view for all transactions"""
    export_type = ExportRequest.ExportType.ALL_TRANSACTIONS


class VirtualAccountTransactionsExportView(BaseExportView):
    """Export view for virtual account transactions"""
    export_type = ExportRequest.ExportType.VIRTUAL_ACCOUNT_TRANSACTIONS


class AirtimeTransactionsExportView(BaseExportView):
    """Export view for airtime transactions"""
    export_type = ExportRequest.ExportType.AIRTIME_TRANSACTIONS


class DataTransactionsExportView(BaseExportView):
    """Export view for data transactions"""
    export_type = ExportRequest.ExportType.DATA_TRANSACTIONS


class ElectricityTransactionsExportView(BaseExportView):
    """Export view for electricity transactions"""
    export_type = ExportRequest.ExportType.ELECTRICITY_TRANSACTIONS


class CableTVTransactionsExportView(BaseExportView):
    """Export view for cable TV transactions"""
    export_type = ExportRequest.ExportType.CABLE_TV_TRANSACTIONS


class BettingTransactionsExportView(BaseExportView):
    """Export view for betting transactions"""
    export_type = ExportRequest.ExportType.BETTING_TRANSACTIONS


class EducationTransactionsExportView(BaseExportView):
    """Export view for education transactions"""
    export_type = ExportRequest.ExportType.EDUCATION_TRANSACTIONS


class KYCTransactionsExportView(BaseExportView):
    """Export view for KYC transactions"""
    export_type = ExportRequest.ExportType.KYC_TRANSACTIONS


class EpinTransactionsExportView(BaseExportView):
    """Export view for epin transactions"""
    export_type = ExportRequest.ExportType.EPIN_TRANSACTIONS


class CommissionsExportView(BaseExportView):
    """Export view for commissions"""
    export_type = ExportRequest.ExportType.COMMISSIONS


class DisputesExportView(BaseExportView):
    """Export view for disputes"""
    export_type = ExportRequest.ExportType.DISPUTES


class AuditLogsExportView(BaseExportView):
    """Export view for audit logs"""
    export_type = ExportRequest.ExportType.AUDIT_LOGS
    
    def post(self, request, *args, **kwargs):
        """Create audit logs export with additional permission check"""
        user = request.user
        
        # Check if user has business
        if not hasattr(user, 'business') or not user.business:
            return Response(
                {"error": "User must be associated with a business"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if user is business owner (only business owners can export audit logs)
        if user != user.business.owner:
            return Response(
                {"error": "Only business owners can export audit logs"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        return super().post(request, *args, **kwargs)


class ExportFieldsView(APIView):
    """View to get available fields for each export type"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, export_type=None):
        """Get available fields for export type"""
        try:
            if export_type:
                # Get fields for specific export type
                export_service = get_export_service(export_type)
                fields = export_service.get_default_fields()
                return Response({
                    "export_type": export_type,
                    "available_fields": fields
                })
            else:
                # Get all export types and their fields
                all_fields = {}
                for exp_type in ExportRequest.ExportType.choices:
                    try:
                        export_service = get_export_service(exp_type[0])
                        all_fields[exp_type[0]] = {
                            "display_name": exp_type[1],
                            "available_fields": export_service.get_default_fields()
                        }
                    except ValueError:
                        continue
                
                return Response({"export_types": all_fields})
                
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error getting export fields: {str(e)}")
            return Response(
                {"error": "Failed to get export fields"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ExportTypesView(APIView):
    """View to get available export types"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get list of available export types"""
        export_types = []
        for choice in ExportRequest.ExportType.choices:
            export_types.append({
                "value": choice[0],
                "display_name": choice[1],
                "description": self._get_export_description(choice[0])
            })
        
        return Response({"export_types": export_types})
    
    def _get_export_description(self, export_type):
        """Get description for export type"""
        descriptions = {
            ExportRequest.ExportType.ALL_TRANSACTIONS: "Export all transaction records",
            ExportRequest.ExportType.VIRTUAL_ACCOUNT_TRANSACTIONS: "Export virtual account transaction records",
            ExportRequest.ExportType.AIRTIME_TRANSACTIONS: "Export airtime purchase transaction records",
            ExportRequest.ExportType.DATA_TRANSACTIONS: "Export data purchase transaction records",
            ExportRequest.ExportType.ELECTRICITY_TRANSACTIONS: "Export electricity bill payment transaction records",
            ExportRequest.ExportType.CABLE_TV_TRANSACTIONS: "Export cable TV subscription transaction records",
            ExportRequest.ExportType.BETTING_TRANSACTIONS: "Export betting account funding transaction records",
            ExportRequest.ExportType.EDUCATION_TRANSACTIONS: "Export education payment transaction records",
            ExportRequest.ExportType.KYC_TRANSACTIONS: "Export KYC verification transaction records",
            ExportRequest.ExportType.EPIN_TRANSACTIONS: "Export epin purchase transaction records",
            ExportRequest.ExportType.COMMISSIONS: "Export commission transaction records",
            ExportRequest.ExportType.DISPUTES: "Export dispute records",
            ExportRequest.ExportType.AUDIT_LOGS: "Export audit log records (business owners only)",
        }
        return descriptions.get(export_type, "Export records")
