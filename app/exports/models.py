from common.models import AuditableModel
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils import timezone


class ExportRequest(AuditableModel):
    """
    Model to track data export requests
    """

    class Status(models.TextChoices):
        PENDING = "pending", "Pending"
        PROCESSING = "processing", "Processing"
        COMPLETED = "completed", "Completed"
        FAILED = "failed", "Failed"
        EXPIRED = "expired", "Expired"

    class ExportType(models.TextChoices):
        # Transaction exports
        ALL_TRANSACTIONS = "all_transactions", "All Transactions"
        VIRTUAL_ACCOUNT_TRANSACTIONS = "virtual_account_transactions", "Virtual Account Transactions"
        AIRTIME_TRANSACTIONS = "airtime_transactions", "Airtime Transactions"
        DATA_TRANSACTIONS = "data_transactions", "Data Transactions"
        ELECTRICITY_TRANSACTIONS = "electricity_transactions", "Electricity Transactions"
        CABLE_TV_TRANSACTIONS = "cable_tv_transactions", "Cable TV Transactions"
        BETTING_TRANSACTIONS = "betting_transactions", "Betting Transactions"
        EDUCATION_TRANSACTIONS = "education_transactions", "Education Transactions"
        KYC_TRANSACTIONS = "kyc_transactions", "KYC Transactions"
        EPIN_TRANSACTIONS = "epin_transactions", "Epin Transactions"

        # Other exports
        COMMISSIONS = "commissions", "Commissions"
        DISPUTES = "disputes", "Disputes"
        AUDIT_LOGS = "audit_logs", "Audit Logs"

    # User who requested the export
    user = models.ForeignKey(
        "user.User", on_delete=models.CASCADE, related_name="export_requests"
    )

    # Business context (for filtering data)
    business = models.ForeignKey(
        "business.Business", on_delete=models.CASCADE, related_name="export_requests"
    )

    # Export details
    export_type = models.CharField(
        max_length=20, choices=ExportType.choices, db_index=True
    )

    # Model being exported
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    model_name = models.CharField(max_length=100, db_index=True)

    # Export configuration
    fields_to_export = models.JSONField(
        help_text="List of field names to include in export"
    )
    filters = models.JSONField(
        default=dict, help_text="Filters applied to the queryset"
    )

    # File details
    filename = models.CharField(max_length=255)
    file_path = models.CharField(max_length=500, null=True, blank=True)
    file_size = models.BigIntegerField(null=True, blank=True)
    download_url = models.URLField(null=True, blank=True)

    # Status tracking
    status = models.CharField(
        max_length=20, choices=Status.choices, default=Status.PENDING, db_index=True
    )

    # Processing details
    total_records = models.IntegerField(null=True, blank=True)
    processed_records = models.IntegerField(default=0)
    error_message = models.TextField(null=True, blank=True)

    # Expiration
    expires_at = models.DateTimeField(
        null=True, blank=True, help_text="When the download link expires"
    )

    # Email notification
    email_sent = models.BooleanField(default=False)
    email_sent_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["business", "-created_at"]),
            models.Index(fields=["status", "-created_at"]),
            models.Index(fields=["export_type", "-created_at"]),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.export_type} - {self.status}"

    @property
    def progress_percentage(self):
        """Calculate export progress percentage"""
        if not self.total_records:
            return 0
        return min(100, (self.processed_records / self.total_records) * 100)

    @property
    def is_expired(self):
        """Check if the download link has expired"""
        if not self.expires_at:
            return False
        return timezone.now() > self.expires_at

    def mark_as_expired(self):
        """Mark the export as expired"""
        self.status = self.Status.EXPIRED
        self.save(update_fields=["status"])


class ExportField(models.Model):
    """
    Model to store available fields for each exportable model
    """

    model_name = models.CharField(max_length=100, db_index=True)
    field_name = models.CharField(max_length=100)
    field_label = models.CharField(max_length=200)
    field_type = models.CharField(max_length=50)
    is_default = models.BooleanField(default=False)
    is_sensitive = models.BooleanField(default=False)
    order = models.IntegerField(default=0)

    class Meta:
        unique_together = ("model_name", "field_name")
        ordering = ["model_name", "order", "field_name"]

    def __str__(self):
        return f"{self.model_name}.{self.field_name}"
