"""
Management command to test the per-action export functionality
"""
import json
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta

from exports.models import ExportRequest
from exports.export_services import get_export_service, EXPORT_SERVICES
from exports.tasks import process_export_task

User = get_user_model()


class Command(BaseCommand):
    help = 'Test the per-action export functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            default='<EMAIL>',
            help='Email of the user to test exports for (default: <EMAIL>)'
        )
        parser.add_argument(
            '--export-type',
            type=str,
            choices=[choice[0] for choice in ExportRequest.ExportType.choices],
            help='Specific export type to test (if not provided, tests all types)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Only show what would be exported without creating actual export requests'
        )
        parser.add_argument(
            '--with-filters',
            action='store_true',
            help='Test exports with sample filters applied'
        )

    def handle(self, *args, **options):
        email = options['email']
        export_type = options.get('export_type')
        dry_run = options['dry_run']
        with_filters = options['with_filters']

        self.stdout.write(
            self.style.SUCCESS(f"🚀 Testing per-action export functionality for user: {email}")
        )

        # Get user
        try:
            user = User.objects.get(email=email)
            if not hasattr(user, 'business') or not user.business:
                raise CommandError(f"User {email} is not associated with a business")
        except User.DoesNotExist:
            raise CommandError(f"User with email {email} does not exist")

        business = user.business
        self.stdout.write(f"📊 Business: {business.name}")

        if export_type:
            # Test specific export type
            self._test_export_type(user, business, export_type, dry_run, with_filters)
        else:
            # Test all export types
            self._test_all_export_types(user, business, dry_run, with_filters)

        self.stdout.write(self.style.SUCCESS("\n✅ Export testing completed!"))

    def _test_export_type(self, user, business, export_type, dry_run, with_filters):
        """Test a specific export type"""
        self.stdout.write(f"\n🔍 Testing export type: {export_type}")
        
        try:
            # Get export service
            export_service = get_export_service(export_type)
            
            # Get default fields
            default_fields = export_service.get_default_fields()
            self.stdout.write(f"   📋 Default fields ({len(default_fields)}): {', '.join(default_fields[:5])}{'...' if len(default_fields) > 5 else ''}")
            
            # Build sample filters
            filters = self._get_sample_filters(export_type) if with_filters else {}
            if filters:
                self.stdout.write(f"   🔧 Sample filters: {json.dumps(filters, indent=2)}")
            
            # Test queryset building
            try:
                queryset = export_service.build_queryset(business, filters)
                record_count = queryset.count()
                self.stdout.write(f"   📊 Records found: {record_count}")
                
                if record_count > 0:
                    # Show sample record
                    sample_record = queryset.first()
                    self.stdout.write(f"   📄 Sample record ID: {sample_record.id}")
                
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f"   ⚠️  Error building queryset: {str(e)}")
                )
                return
            
            if not dry_run and record_count > 0:
                # Create actual export request
                try:
                    export_request = export_service.create_export_request(
                        user=user,
                        business=business,
                        fields_to_export=default_fields[:10],  # Limit fields for testing
                        filters=filters
                    )
                    
                    self.stdout.write(
                        self.style.SUCCESS(f"   ✅ Export request created: {export_request.id}")
                    )
                    
                    # Start processing (optional)
                    self.stdout.write("   🔄 Starting background processing...")
                    process_export_task.delay(export_request.id)
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"   ❌ Error creating export request: {str(e)}")
                    )
            elif dry_run:
                self.stdout.write("   🔍 Dry run - no export request created")
            else:
                self.stdout.write("   📭 No records to export")
                
        except ValueError as e:
            self.stdout.write(
                self.style.ERROR(f"   ❌ Export service error: {str(e)}")
            )

    def _test_all_export_types(self, user, business, dry_run, with_filters):
        """Test all available export types"""
        self.stdout.write(f"\n🔍 Testing all {len(EXPORT_SERVICES)} export types:")
        
        results = {
            'successful': [],
            'failed': [],
            'no_data': []
        }
        
        for export_type in EXPORT_SERVICES.keys():
            try:
                self.stdout.write(f"\n📋 {export_type}:")
                
                # Get export service
                export_service = get_export_service(export_type)
                
                # Test queryset building
                filters = self._get_sample_filters(export_type) if with_filters else {}
                queryset = export_service.build_queryset(business, filters)
                record_count = queryset.count()
                
                self.stdout.write(f"   📊 Records: {record_count}")
                
                if record_count > 0:
                    results['successful'].append(export_type)
                    
                    if not dry_run:
                        # Create export request for types with data
                        default_fields = export_service.get_default_fields()
                        export_request = export_service.create_export_request(
                            user=user,
                            business=business,
                            fields_to_export=default_fields[:5],  # Limit for testing
                            filters=filters
                        )
                        self.stdout.write(f"   ✅ Export created: {export_request.id}")
                else:
                    results['no_data'].append(export_type)
                    self.stdout.write("   📭 No data")
                    
            except Exception as e:
                results['failed'].append(export_type)
                self.stdout.write(
                    self.style.WARNING(f"   ⚠️  Error: {str(e)}")
                )
        
        # Summary
        self.stdout.write(f"\n📈 Summary:")
        self.stdout.write(f"   ✅ Successful: {len(results['successful'])}")
        self.stdout.write(f"   📭 No data: {len(results['no_data'])}")
        self.stdout.write(f"   ❌ Failed: {len(results['failed'])}")
        
        if results['failed']:
            self.stdout.write(f"   Failed types: {', '.join(results['failed'])}")

    def _get_sample_filters(self, export_type):
        """Get sample filters for testing"""
        # Common date filter (last 30 days)
        date_from = (timezone.now() - timedelta(days=30)).isoformat()
        date_to = timezone.now().isoformat()
        
        base_filters = {
            'date_from': date_from,
            'date_to': date_to
        }
        
        # Add type-specific filters
        type_specific_filters = {
            ExportRequest.ExportType.ALL_TRANSACTIONS: {
                'status': 'SUCCESS',
                'amount_min': '100'
            },
            ExportRequest.ExportType.AIRTIME_TRANSACTIONS: {
                'status': 'SUCCESS',
                'network': 'MTN'
            },
            ExportRequest.ExportType.ELECTRICITY_TRANSACTIONS: {
                'status': 'SUCCESS',
                'disco': 'EKEDC'
            },
            ExportRequest.ExportType.DISPUTES: {
                'status': 'PENDING'
            },
            ExportRequest.ExportType.AUDIT_LOGS: {
                'action': 'LOGIN'
            }
        }
        
        specific_filters = type_specific_filters.get(export_type, {})
        return {**base_filters, **specific_filters}
