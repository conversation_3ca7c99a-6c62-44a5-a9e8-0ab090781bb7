from enum import Enum

ROLE_OPTIONS = (
    ("Admin", "Admin"),
    ("Business_Owner", "Business Owner"),
    ("Team_Member", "Team Member"),
    ("Initiator", "Initiator"),
    ("Verifier", "Verifier"),
    ("Approver", "Approver"),
    ("Merchant_Admin", "Merchant Admin"),
    ("Customer_Support", "Customer Support"),
    ("Operations", "Operations"),
    ("Reconciliation", "Reconciliation"),
    ("Developer", "Developer"),
)

GENDER_OPTION = (
    ("Male", "Male"),
    ("Female", "Female"),
)

TOKEN_TYPE = (
    ("CreateToken", "CreateToken"),
    ("ResetToken", "ResetToken"),
)


class PinEnum(Enum):
    Transaction = "Transaction"
    Transfer = "Transfer"


class Auth2FATypeEnums:
    TOTP = "TOTP"
    EMAIL = "EMAIL"
    CHOICES = [
        (TOTP, TOTP),
        (EMAIL, EMAIL),
    ]
