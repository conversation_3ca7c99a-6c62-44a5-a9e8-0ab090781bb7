import logging
from datetime import datetime

import jwt
from audit.models import <PERSON>t<PERSON>og
from audit.utils import (  # log_logout,; log_profile_update,
    log_login_attempt,
    log_otp_action,
    log_password_change,
    log_registration,
    log_user_action,
)
from business.models import Business
from business.tasks import initialize_business_configurations
from django.conf import settings
from django.contrib.auth import authenticate, get_user_model
from django.contrib.auth.hashers import check_password, make_password
from django.core.cache.backends.base import DEFAULT_TIMEOUT
from django.db import transaction
from django.utils import timezone
from django.utils.timezone import make_aware
from django_filters.rest_framework import DjangoFilterBackend
from google.auth.transport import requests as google_requests
from google.oauth2 import id_token
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from teams.enums import SystemBaseUserRole, TeamMemberStatus
from teams.models import TeamMember
from user.decorators import jwt_required
from user.models import Token, User
from user.tasks import (
    send_login_otp_email,
    send_new_user_welcome_email,
    send_password_reset_email,
    send_registration_email,
)
from user.utils import cache_user_session_key, generate_token
from user.v1.serializers import (
    Auth2FASetupSerializer,
    Authenticate2FALoginSerializer,
    BusinessUserRegistrationSerializer,
    CreatePasswordSerializer,
    CreateUserSerializer,
    CustomObtainTokenPairSerializer,
    Finalize2FASetupSerializer,
    GoogleAuthSerializer,
    InitPasswordResetSerializer,
    LoginRequestSerializer,
    OTPVerificationSerializer,
    PinSerializer,
    TokenDecodeSerializer,
    UserSerializer,
    VerifyTokenSerializer,
)

CACHE_TTL = getattr(settings, "CACHE_TTL", DEFAULT_TIMEOUT)
logger = logging.getLogger(__name__)


class DecodeJwtTokenView(APIView):
    serializer_class = TokenDecodeSerializer

    @jwt_required  # only a valid token can access this view
    def post(self, request):
        # print("META", request.META)
        # print("PAYLOAD", request.data)
        token = request.data.get("token", None)
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms="HS256")
            except jwt.ExpiredSignatureError as e:
                logger.error(e)
                raise AuthenticationFailed("Unauthenticated")

            user = User.objects.get(id=payload["user_id"])
            serializer = UserSerializer(instance=user)
            return Response(
                {
                    **serializer.data,
                    # "tenant_id": payload["id"],
                    # "permissions": user.permission_list(),
                }
            )
        raise AuthenticationFailed("Unauthenticated")


class UserViewSets(viewsets.ModelViewSet):
    """User viewsets"""

    queryset = get_user_model().objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    http_method_names = ["get", "post", "patch", "delete"]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = [
        "is_active",
        "role",
    ]
    search_fields = ["email", "firstname", "lastname", "phone"]
    ordering_fields = [
        "created_at",
        "email",
        "firstname",
        "lastname",
    ]

    def create(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def partial_update(self, request, *args, **kwargs):
        """Handle profile updates with audit logging"""
        instance = self.get_object()

        # Store old values for audit logging
        old_data = {
            "firstname": instance.firstname,
            "lastname": instance.lastname,
            "phone": instance.phone,
            "image": str(instance.image) if instance.image else None,
        }

        # Perform the update
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        updated_instance = serializer.save()

        # Store new values for audit logging
        new_data = {
            "firstname": updated_instance.firstname,
            "lastname": updated_instance.lastname,
            "phone": updated_instance.phone,
            "image": str(updated_instance.image) if updated_instance.image else None,
        }

        # Log profile update
        from audit.utils import log_profile_update

        log_profile_update(
            request=request, user=request.user, old_data=old_data, new_data=new_data
        )

        return Response(serializer.data)

    def get_permissions(self):
        permission_classes = self.permission_classes
        if self.action in [
            "create_password",
            "initialize_reset",
            "verify_token",
            "retrieve",
            "list",
        ]:
            permission_classes = [AllowAny]
        elif self.action in ["destroy", "partial_update"]:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    @action(
        methods=["POST"],
        detail=False,
        serializer_class=CreateUserSerializer,
        url_path="invite-user",
    )
    def invite_user(self, request, pk=None):
        """This endpoint invites new user"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Log team member invitation
        from audit.utils import log_team_member_invitation

        business = getattr(request.user, "business", None)
        log_team_member_invitation(
            request=request,
            user=request.user,
            invited_email=user.email,
            role=user.role,
            business=business,
        )

        return Response(
            {"success": True, "data": serializer.data}, status=status.HTTP_200_OK
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="logout",
        permission_classes=[IsAuthenticated],
    )
    def logout(self, request):
        """Logout endpoint with audit logging"""
        user = request.user

        # Log logout action
        from audit.utils import log_admin_logout, log_logout

        # Determine if this is an admin logout
        admin_roles = ["Admin", "Super_Admin", "System_Admin"]
        if user.role in admin_roles:
            log_admin_logout(request=request, user=user)
        else:
            log_logout(request=request, user=user)

        return Response(
            {"success": True, "message": "Logged out successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        serializer_class=VerifyTokenSerializer,
        url_path="verify-token",
        permission_classes=[AllowAny],
    )
    def verify_token(self, request, pk=None):
        """This endpoint verifies token"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            token = Token.objects.filter(token=request.data.get("token")).first()
            if token and token.is_valid():
                return Response(
                    {"success": True, "valid": True}, status=status.HTTP_200_OK
                )
            return Response(
                {"success": False, "valid": False}, status=status.HTTP_404_NOT_FOUND
            )
        return Response(
            {"success": False, "errors": serializer.errors}, status.HTTP_400_BAD_REQUEST
        )

    @action(
        methods=["POST"],
        detail=False,
        serializer_class=InitPasswordResetSerializer,
        url_path="reset-password",
        permission_classes=[AllowAny],
    )
    def initialize_reset(self, request, pk=None):
        """This endpoint initializes password reset by sending password reset email to user"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            email = request.data["email"].lower().strip()
            user = get_user_model().objects.filter(email=email, is_active=True).first()
            if not user:
                return Response(
                    {
                        "success": False,
                        "message": "User with this email does not exist",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Delete any existing reset tokens for this user to ensure only the latest one is valid
            Token.objects.filter(user=user, token_type="ResetToken").delete()

            # Create a new reset token
            token = Token.objects.create(
                user=user,
                token=generate_token(user),
                token_type="ResetToken",
            )

            email_data = {
                "fullname": user.firstname,
                "email": user.email,
                "token": token.token,
                "url": f"{settings.CLIENT_URL}/reset-password/?token={token.token}",
            }
            send_password_reset_email.delay(email_data)
            return Response(
                {
                    "success": True,
                    "message": "Email successfully sent to registered email",
                },
                status=status.HTTP_200_OK,
            )
        return Response(
            {"success": False, "errors": serializer.errors}, status.HTTP_400_BAD_REQUEST
        )

    @action(
        methods=["POST"],
        detail=False,
        serializer_class=CreatePasswordSerializer,
        url_path="create-password",
    )
    def create_password(self, request, pk=None):
        """Create a new password given the reset token"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            data = serializer.validated_data
            token_data = data.pop("token")
            token = Token.objects.filter(token=token_data).first()
            if not token or not token.is_valid():
                return Response(
                    {"success": False, "errors": "Invalid token specified"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            user = User.objects.get(id=token.user.id)
            current_timestamp = make_aware(datetime.now())
            user.set_password(data["password"])
            if not user.verified:
                user.verified = True
                user.email_verified_at = current_timestamp
            user.save()
            token.delete()

            if user.role == SystemBaseUserRole.TeamMember.value:
                instance = TeamMember.objects.filter(user=user).first()
                if instance:
                    instance.status = TeamMemberStatus.Active.value
                    instance.joined_at = current_timestamp
                    instance.save()
            return Response(
                {"success": True, "message": "Password successfully set"},
                status=status.HTTP_200_OK,
            )
        return Response(
            {"success": False, "errors": serializer.errors}, status.HTTP_400_BAD_REQUEST
        )

    @action(
        methods=["POST"],
        detail=True,
        serializer_class=PinSerializer,
        url_path="create-pin",
    )
    def create_pin(self, request, pk=None):
        user = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user.transaction_pin = make_password(serializer.validated_data["pin"])
        user.save()
        return Response(
            {"success": True, "message": "Pin set successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        serializer_class=PinSerializer,
        url_path="verify-pin",
    )
    def verify_pin(self, request, pk=None):
        user = self.request.user
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        transaction_pin = user.transaction_pin
        is_valid = (
            check_password(serializer.validated_data["pin"], transaction_pin)
            if transaction_pin
            else False
        )
        if is_valid:
            return Response(
                {"success": True, "message": "Pin is valid"}, status=status.HTTP_200_OK
            )
        return Response(
            {"success": False, "errors": "Invalid Pin"},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    @action(
        methods=["POST"],
        detail=False,
        serializer_class=LoginRequestSerializer,
        url_path="login-request",
        permission_classes=[AllowAny],
    )
    def login_request(self, request):
        """Request OTP for login"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data["email"].lower().strip()
        password = serializer.validated_data["password"]

        user = authenticate(request=request, username=email, password=password)

        if not user:
            # Log failed login attempt
            log_login_attempt(
                request, user=None, success=False, reason="Invalid credentials"
            )
            return Response(
                {"success": False, "message": "Invalid credentials"},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        if not user.is_active:
            # Log failed login attempt for disabled account
            log_login_attempt(
                request, user=user, success=False, reason="Account disabled"
            )
            return Response(
                {"success": False, "message": "User account is disabled"},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        # Generate and send OTP
        otp = user.generate_otp()

        email_data = {"fullname": user.fullname, "email": user.email, "otp": otp}

        send_login_otp_email.delay(email_data)

        # Log OTP request
        log_otp_action(request, user=user, action_type="REQUEST", success=True)

        return Response(
            {"success": True, "message": "OTP sent to your email", "email": user.email},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        serializer_class=OTPVerificationSerializer,
        url_path="verify-login",
        permission_classes=[AllowAny],
    )
    def verify_login(self, request):
        """Verify OTP and complete login"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data["email"].lower().strip()
        otp = serializer.validated_data["otp"]

        user = User.objects.filter(email=email).first()

        if not user:
            # Log failed OTP verification - user not found
            log_otp_action(request, user=None, action_type="VERIFY", success=False)
            return Response(
                {"success": False, "message": "User not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        if not user.verify_otp(otp):
            # Log failed OTP verification
            log_otp_action(request, user=user, action_type="VERIFY", success=False)
            return Response(
                {"success": False, "message": "Invalid or expired OTP"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Clear OTP after successful verification
        user.otp = None
        user.otp_created_at = None
        user.save()

        # Generate tokens
        refresh = RefreshToken.for_user(user)
        refresh["email"] = user.email
        refresh["role"] = user.role
        refresh["fullname"] = user.fullname
        refresh["phone"] = user.phone

        user.save_last_login()

        # Log successful login (differentiate admin vs regular user)
        admin_roles = ["Admin", "Super_Admin", "System_Admin"]
        if user.role in admin_roles:
            from audit.utils import log_admin_login

            log_admin_login(request, user=user, success=True)
        else:
            log_login_attempt(request, user=user, success=True)
        log_otp_action(request, user=user, action_type="VERIFY", success=True)

        return Response(
            {
                "success": True,
                "message": "Login successful",
                "refresh": str(refresh),
                "access": str(refresh.access_token),
                "user": {
                    "id": user.id,
                    "email": user.email,
                    "fullname": user.fullname,
                    "role": user.role,
                },
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        serializer_class=BusinessUserRegistrationSerializer,
        url_path="register-business",
        permission_classes=[AllowAny],
    )
    def register_business(self, request):
        """Register a new business user"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        business_name = validated_data.pop("business_name")

        # Set role to business owner
        validated_data["role"] = "Business_Owner"

        with transaction.atomic():
            user = User.objects.create_user(
                **validated_data, password=make_password(None)
            )
            business = Business.objects.create(name=business_name, owner=user)
            business._create_core_wallets()
            initialize_business_configurations.delay_on_commit(business.id)

            token_str = generate_token(user)
            token, _ = Token.objects.update_or_create(
                user=user,
                token_type="CreateToken",
                defaults={
                    "user": user,
                    "token_type": "CreateToken",
                    "token": token_str,
                },
            )

        email_data = {
            "fullname": user.fullname,
            "email": user.email,
            "url": f"{settings.CLIENT_URL}/verify-user/?token={token.token}",
            "token": token.token,
        }
        send_registration_email.delay(email_data)

        # Log successful registration
        log_registration(request, user=user, registration_type="business_email")

        return Response(
            {
                "success": True,
                "message": "Registration successful. Verification link sent to your email.",
                "email": user.email,
            },
            status=status.HTTP_201_CREATED,
        )

    @action(
        methods=["POST"],
        detail=False,
        serializer_class=CreatePasswordSerializer,
        url_path="create-account-password",
        permission_classes=[AllowAny],
    )
    def create_account_password(self, request):
        """Create password for a new account after token verification"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        data = serializer.validated_data
        token_data = data.pop("token")
        password = data["password"]

        token = Token.objects.filter(token=token_data).first()
        if not token or not token.is_valid():
            return Response(
                {"success": False, "errors": "Invalid or expired token"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        user = User.objects.get(id=token.user.id)

        # Set password and mark as verified
        user.set_password(password)
        user.is_password_set = True
        user.password_last_changed_at = timezone.now()
        user.verified = True
        user.email_verified_at = timezone.now()
        user.save()

        # Delete the token after use
        token.delete()

        # Generate tokens for automatic login
        refresh = RefreshToken.for_user(user)
        refresh["email"] = user.email
        refresh["role"] = user.role
        refresh["fullname"] = user.fullname
        refresh["phone"] = user.phone

        user.save_last_login()

        # Log password creation and successful login
        log_password_change(request, user=user, success=True)
        log_login_attempt(request, user=user, success=True)

        return Response(
            {
                "success": True,
                "message": "Account setup completed successfully",
                "refresh": str(refresh),
                "access": str(refresh.access_token),
                "user": {
                    "id": user.id,
                    "email": user.email,
                    "fullname": user.fullname,
                    "role": user.role,
                },
            },
            status=status.HTTP_200_OK,
        )


class CustomObtainTokenPairView(TokenObtainPairView):
    """Login with email and password"""

    serializer_class = CustomObtainTokenPairSerializer


class Setup2FAView(APIView):
    """
    Setup 2FA for a user.

    NOTE:
    - QR code won't be shown again once the process is complete (for security reasons).
    """

    serializer_class = Auth2FASetupSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {
                "success": True,
                "message": "QR Code generated successfully for 2FA.",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class Finalize2FASetupView(APIView):
    """
    Verify OTP for the user.

    NOTE:
    - This only verifies OTP and is based on the user's auth_type.
    """

    serializer_class = Finalize2FASetupSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(
            {
                "success": True,
                "message": "2FA setup completed successfully",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )


class Authenticate2FALoginView(APIView):
    """
    Authenticate user with login details via 2FA.

    NOTE:
    - If "two_factor_auth_enabled" is false, redirect the user to set up 2FA using an authenticator app.
    """

    serializer_class = Authenticate2FALoginSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.to_representation(serializer.validated_data)
        return Response(
            {"success": True, "message": "Login successful", "data": data},
            status=status.HTTP_200_OK,
        )


class GoogleAuthView(APIView):
    def post(self, request):
        logger.info("Received Google Auth Request")
        serializer = GoogleAuthSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        token = serializer.validated_data["id_token"]

        try:
            logger.debug("Verifying Google ID token")
            idinfo = id_token.verify_oauth2_token(
                token, google_requests.Request(), settings.GOOGLE_CLIENT_ID
            )
            if idinfo["aud"] != settings.GOOGLE_CLIENT_ID:
                logger.warning("Client ID mismatch: %s", idinfo["aud"])
                return Response(
                    {"detail": "Invalid Client Detected."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not idinfo.get("email_verified"):
                logger.warning("Unverified email attempt: %s", idinfo.get("email"))
                return Response(
                    {"detail": "Email not verified."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            sub = idinfo["sub"]
            email = idinfo["email"]
            firstname = idinfo.get("given_name", "")
            lastname = idinfo.get("family_name", "")

            try:
                user = User.objects.get(google_sub=sub)
                logger.info("Existing user authenticated via Google: %s", user.email)
                # Log successful Google authentication for existing user
                log_user_action(
                    request=request,
                    user=user,
                    action=AuditLog.GOOGLE_AUTH,
                    description="User authenticated via Google (existing account)",
                    resource_type="User",
                    resource_id=user.id,
                )
            except User.DoesNotExist:
                if User.objects.filter(email=email).exists():
                    logger.warning("Email collision detected for: %s", email)
                    return Response(
                        {
                            "detail": "Email already in use. Login with your password and link your Google account."
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                user = User.objects.create(
                    email=email,
                    firstname=firstname,
                    lastname=lastname,
                    google_sub=sub,
                    verified=True,
                    is_google_account_linked=True,
                    role="Business_Owner",
                    password=make_password(None),
                )
                logger.info("New user created via Google: %s", email)

                business = Business.objects.create(owner=user)
                business._create_core_wallets()

                email_data = {
                    "fullname": user.fullname,
                    "email": user.email,
                    "url": f"{settings.CLIENT_URL}/overview",
                }
                logger.debug("Sending welcome email: %s", email_data)
                send_new_user_welcome_email.delay(email_data)

                # Log successful Google registration for new user
                log_registration(request, user=user, registration_type="google")
                log_user_action(
                    request=request,
                    user=user,
                    action=AuditLog.GOOGLE_AUTH,
                    description="New user registered and authenticated via Google",
                    resource_type="User",
                    resource_id=user.id,
                )

            session_id = cache_user_session_key(user.id, 600)
            logger.debug("Session ID issued for user %s: %s", user.email, session_id)

            data = {
                "session_id": session_id,
                "two_factor_auth_type": user.two_factor_auth_type,
                "two_factor_auth_enabled": user.two_factor_auth_enabled,
            }
            return Response(
                {
                    "success": True,
                    "message": "Google Sign-In successful",
                    "data": data,
                },
                status=status.HTTP_200_OK,
            )

        except ValueError:
            logger.exception("Invalid token during Google OAuth verification")
            return Response(
                {"mesage": "Invalid token."}, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.exception(
                "An error occurred during Google OAuth verification, %s", str(e)
            )
            return Response(
                {"message": "An error occurred."}, status=status.HTTP_400_BAD_REQUEST
            )
        finally:
            logger.info("Google Auth process completed")


class UserRoleInfoView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        data = {
            "system_role": user.role,
            "team_role_name": None,
            "permissions": [],
        }

        if user.role == SystemBaseUserRole.TeamMember.value:
            try:
                team_member = user.teammember
                data["team_role_name"] = team_member.role.name
                data["permissions"] = list(
                    team_member.role.permissions.values_list("codename", flat=True)
                )
            except TeamMember.DoesNotExist:
                pass

        return Response(data)
