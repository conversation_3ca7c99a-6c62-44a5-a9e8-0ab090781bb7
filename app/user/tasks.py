from core.celery import APP
from django.template.loader import get_template

from .utils import send_email


@APP.task()
def send_registration_email(email_data):
    html_template = get_template("emails/account_verification_template.html")
    text_template = get_template("emails/account_verification_template.txt")
    html_alternative = html_template.render(email_data)
    text_alternative = text_template.render(email_data)
    send_email(
        "Account Verification", email_data["email"], html_alternative, text_alternative
    )


@APP.task()
def send_merchant_teams_registration_email(email_data):
    role_name = email_data.get("role_name", "Team Member")
    html_template = get_template("emails/teams_account_verification_template.html")
    text_template = get_template("emails/teams_account_verification_template.txt")
    html_alternative = html_template.render(email_data)
    text_alternative = text_template.render(email_data)
    send_email(
        f"🎉 You’ve Been Added to Sagecloud as a {role_name}",
        email_data["email"],
        html_alternative,
        text_alternative,
    )


@APP.task()
def send_new_user_welcome_email(email_data):
    html_template = get_template("emails/new_user_welcome_template.html")
    text_template = get_template("emails/new_user_welcome_template.txt")
    html_alternative = html_template.render(email_data)
    text_alternative = text_template.render(email_data)
    send_email(
        "Welcome to SageCloud", email_data["email"], html_alternative, text_alternative
    )


@APP.task()
def send_password_reset_email(email_data):
    html_template = get_template("emails/password_reset_template.html")
    text_template = get_template("emails/password_reset_template.txt")
    html_alternative = html_template.render(email_data)
    text_alternative = text_template.render(email_data)
    send_email(
        "Password Reset", email_data["email"], html_alternative, text_alternative
    )


@APP.task()
def send_login_otp_email(email_data):
    html_template = get_template("emails/login_otp_template.html")
    text_template = get_template("emails/login_otp_template.txt")
    html_alternative = html_template.render(email_data)
    text_alternative = text_template.render(email_data)
    send_email(
        "Login Verification Code",
        email_data["email"],
        html_alternative,
        text_alternative,
    )
