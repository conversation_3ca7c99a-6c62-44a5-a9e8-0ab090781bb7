from decimal import Decimal

from common.enums import ProductEnum, ProviderEnum, ServiceEnum
from common.models import AuditableModel
from django.db import models
from fees.enums import BandedFeeType, FeeType


class BusinessFee(AuditableModel):
    business = models.ForeignKey(
        "business.Business", on_delete=models.CASCADE, related_name="business_fee"
    )

    product = models.CharField(
        choices=ProductEnum.choices(), max_length=50, db_index=True
    )
    service = models.CharField(
        choices=ServiceEnum.choices(), max_length=50, db_index=True
    )

    fee_type = models.CharField(max_length=20, choices=FeeType.choices())
    amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    cap_amount = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )


class BusinessFeeBand(AuditableModel):
    business_fee = models.ForeignKey(
        "fees.BusinessFee", on_delete=models.CASCADE, related_name="business_fee_bands"
    )

    amount = models.DecimalField(max_digits=12, decimal_places=2)
    fee_type = models.CharField(
        max_length=20,
        choices=BandedFeeType.choices(),
        default=BandedFeeType.Fixed.value,
    )

    lower_bound = models.DecimalField(max_digits=12, decimal_places=2)
    upper_bound = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )
    upper_bound_infinite = models.BooleanField(default=False)


class ProviderFee(AuditableModel):
    provider = models.CharField(
        choices=ProviderEnum.choices(), max_length=50, db_index=True
    )

    product = models.CharField(
        choices=ProductEnum.choices(), max_length=50, db_index=True
    )
    service = models.CharField(
        choices=ServiceEnum.choices(), max_length=50, db_index=True
    )

    fee_type = models.CharField(max_length=20, choices=FeeType.choices())
    amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    cap_amount = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )


class ProviderFeeBand(AuditableModel):
    provider_fee = models.ForeignKey(
        "fees.ProviderFee", on_delete=models.CASCADE, related_name="provider_fee_bands"
    )

    amount = models.DecimalField(max_digits=12, decimal_places=2)
    fee_type = models.CharField(
        max_length=20,
        choices=BandedFeeType.choices(),
        default=BandedFeeType.Fixed.value,
    )

    lower_bound = models.DecimalField(max_digits=12, decimal_places=2)
    upper_bound = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )
    upper_bound_infinite = models.BooleanField(default=False)


class GeneralFee(AuditableModel):
    product = models.CharField(
        choices=ProductEnum.choices(), max_length=50, db_index=True
    )
    service = models.CharField(
        choices=ServiceEnum.choices(), max_length=50, db_index=True
    )

    fee_type = models.CharField(max_length=20, choices=FeeType.choices())
    amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    cap_amount = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )


class GeneralFeeBand(AuditableModel):
    general_fee = models.ForeignKey(
        "fees.GeneralFee", on_delete=models.CASCADE, related_name="general_fee_bands"
    )

    amount = models.DecimalField(max_digits=12, decimal_places=2)
    fee_type = models.CharField(
        max_length=20,
        choices=BandedFeeType.choices(),
        default=BandedFeeType.Fixed.value,
    )

    lower_bound = models.DecimalField(max_digits=12, decimal_places=2)
    upper_bound = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )
    upper_bound_infinite = models.BooleanField(default=False)
