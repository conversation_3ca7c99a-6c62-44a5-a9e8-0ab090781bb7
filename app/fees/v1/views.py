from itertools import groupby
from operator import attrgetter

from business.models import Business
from django_filters.rest_framework import DjangoFilterBackend
from fees.filter import Provider<PERSON>eeFilter
from fees.models import <PERSON><PERSON>ee, GeneralFee, ProviderFee
from fees.v1.serializers import (
    ApplyFeesToAllMerchantsSerializer,
    BusinessFeeSerializer,
    CreateBulkBusinessFeeSerializer,
    CreateBulkGeneralFeeSerializer,
    CreateBulkProviderFeeSerializer,
    GeneralFeeSerializer,
    ProviderFeeSerializer,
)
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.response import Response


class BusinessFeeViewSet(viewsets.ModelViewSet):
    queryset = BusinessFee.objects.prefetch_related("business_fee_bands").all()
    serializer_class = BusinessFeeSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == "Business_Owner":
            queryset = queryset.filter(business=self.request.user.business)
        return queryset

    def get_serializer_class(self):
        if self.action == "create":
            return CreateBulkBusinessFeeSerializer
        return BusinessFeeSerializer

    def list(self, request, *args, **kwargs):
        business: Business = request.user.business
        queryset = (
            BusinessFee.objects.filter(business=business)
            .prefetch_related("business_fee_bands")
            .order_by("product")
        )
        grouped_fees = {
            product: BusinessFeeSerializer(list(items), many=True).data
            for product, items in groupby(queryset, key=attrgetter("product"))
        }

        return Response(grouped_fees, status=status.HTTP_200_OK)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Fees created/updated successfully"},
            status=status.HTTP_201_CREATED,
        )

    def update(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)


class ProviderFeeViewSet(viewsets.ModelViewSet):
    queryset = ProviderFee.objects.prefetch_related("provider_fee_bands").all()
    serializer_class = ProviderFeeSerializer
    filterset_class = ProviderFeeFilter
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ["provider"]

    def get_serializer_class(self):
        if self.action == "create":
            return CreateBulkProviderFeeSerializer
        return ProviderFeeSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Fees created/updated successfully"},
            status=status.HTTP_201_CREATED,
        )

    def update(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)


class GeneralFeeViewSet(viewsets.ModelViewSet):
    queryset = GeneralFee.objects.prefetch_related("general_fee_bands").all()
    serializer_class = GeneralFeeSerializer

    def get_serializer_class(self):
        if self.action == "create":
            return CreateBulkGeneralFeeSerializer
        if self.action == "apply_to_all_businesses":
            return ApplyFeesToAllMerchantsSerializer
        return GeneralFeeSerializer

    def list(self, request, *args, **kwargs):
        queryset = GeneralFee.objects.prefetch_related("general_fee_bands").order_by(
            "product"
        )
        grouped_fees = {
            product: GeneralFeeSerializer(list(items), many=True).data
            for product, items in groupby(queryset, key=attrgetter("product"))
        }

        return Response(grouped_fees, status=status.HTTP_200_OK)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Fees created/updated successfully"},
            status=status.HTTP_201_CREATED,
        )

    def update(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @action(
        methods=["POST"],
        detail=False,
        url_path="apply-to-all-businesses",
    )
    def apply_to_all_businesses(self, request, *args, **kwargs):
        serializer = ApplyFeesToAllMerchantsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Applying fees to all merchants..."}
        )
