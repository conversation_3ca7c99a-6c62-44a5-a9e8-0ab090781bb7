from decimal import Decimal

from common.kgs import generate_uuid7
from django.db import transaction
from fees.handlers.fee_processor_handler import FeeProcessorHandler
from ledger.enums import LedgerTypeEnum
from ledger.service import LedgerService
from transaction.models.base import CommissionTransaction


class ProcessCommissionHandler:

    def handle(self, source_transaction, business):
        fee_handler = FeeProcessorHandler(
            amount=source_transaction.amount,
            product=source_transaction.txn_class.capitalize(),
            service=source_transaction.type.capitalize(),
            business=business,
            provider=source_transaction.provider,
        )

        business_comm_value = fee_handler.get_business_fee()

        if business_comm_value <= Decimal(0):
            return None

        provider_comm_value = fee_handler.get_provider_fee()

        revenue = provider_comm_value - business_comm_value

        with transaction.atomic():

            source_transaction.revenue = revenue
            source_transaction.save()

            commission_wallet = business.get_commission_wallet(for_update=True)

            wallet_result = self.__process_wallet_credit(
                commission_wallet, business_comm_value
            )

            comm_txn = CommissionTransaction.objects.create(
                source_transaction=source_transaction,
                source_transaction_reference=source_transaction.reference,
                reference=generate_uuid7(),
                business=source_transaction.business,
                txn_class=source_transaction.txn_class,
                narration=f"Commission for {source_transaction.narration}",
                wallet=commission_wallet,
                old_balance=wallet_result["old_balance"],
                new_balance=wallet_result["new_balance"],
                amount=business_comm_value,
            )

            LedgerService().debit(
                ledger_type=LedgerTypeEnum.AIRTIME_COMMISSION,
                source_commission=comm_txn,
            )

            return {
                "revenue": revenue,
                "provider_comm_value": provider_comm_value,
                "business_comm_value": business_comm_value,
            }

    @staticmethod
    def __process_wallet_credit(wallet, business_commission_val):
        old_balance = wallet.balance

        wallet.credit(business_commission_val)

        # Record new balance
        new_balance = wallet.balance

        balance_info = {
            "old_balance": old_balance,
            "new_balance": new_balance,
        }

        return balance_info
