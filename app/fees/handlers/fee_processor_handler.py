from decimal import Decimal

from fees.enums import BandedFeeType, FeeType
from fees.models import BusinessFee, BusinessFeeBand, ProviderFee, ProviderFeeBand


class FeeProcessorHandler:
    def __init__(
        self, amount: Decimal, product: str, service: str, business=None, provider=None
    ):
        self.amount = amount
        self.product = product
        self.service = service
        self.business = business
        self.provider = provider

    def get_business_fee(self) -> Decimal:

        fee = BusinessFee.objects.filter(
            business=self.business, product=self.product, service=self.service
        ).first()

        if not fee:
            return Decimal("0")

        if fee.fee_type == FeeType.Fixed.value:
            return fee.amount

        elif fee.fee_type == FeeType.Percentage.value:
            fee_value = self.amount * fee.amount
            return self._apply_cap(fee_value, fee.cap_amount)

        elif fee.fee_type == FeeType.Banded.value:
            return self._get_banded_fee(
                BusinessFeeBand.objects.filter(business_fee=fee)
            )

        return Decimal("0")

    def get_provider_fee(self) -> Decimal:
        try:
            fee = ProviderFee.objects.get(
                provider=self.provider, product=self.product, service=self.service
            )
        except ProviderFee.DoesNotExist:
            return Decimal("0")

        if fee.fee_type == FeeType.Fixed.value:
            return self._apply_cap(fee.amount, fee.cap_amount)

        elif fee.fee_type == FeeType.Percentage.value:
            fee_value = self.amount * fee.amount
            return self._apply_cap(fee_value, fee.cap_amount)

        elif fee.fee_type == FeeType.Banded.value:
            return self._get_banded_fee(
                ProviderFeeBand.objects.filter(provider_fee=fee)
            )

        return Decimal("0")

    def _get_banded_fee(self, queryset) -> Decimal:
        for band in queryset:
            lower = band.lower_bound
            upper = band.upper_bound
            infinite = band.upper_bound_infinite
            fee_type = band.fee_type

            if (lower <= self.amount <= upper) or (infinite and self.amount >= lower):

                if fee_type == BandedFeeType.Fixed.value:
                    return band.amount

                if fee_type == BandedFeeType.Percentage.value:
                    return self.amount * band.amount

        return Decimal("0")

    @staticmethod
    def _apply_cap(fee_value: Decimal, cap: Decimal) -> Decimal:
        fee_value = round(fee_value / 100, 2)
        if cap and cap > 0:
            return min(fee_value, cap)
        return fee_value
