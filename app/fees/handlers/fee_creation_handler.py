from django.db import transaction
from fees.enums import BandedFeeType, FeeType
from fees.models import (
    BusinessFee,
    BusinessFeeBand,
    GeneralFee,
    GeneralFeeBand,
    ProviderFee,
    ProviderFeeBand,
)


class FeeHandler:
    @staticmethod
    def create_business_fee(data):
        with transaction.atomic():
            business_fee, _ = BusinessFee.objects.update_or_create(
                business=data["business"],
                product=data["product"],
                service=data["service"],
                defaults={
                    "fee_type": data["fee_type"],
                    "amount": data.get("amount", 0),
                    "cap_amount": data.get("cap_amount", 0),
                },
            )

            if data["fee_type"] == FeeType.Banded.value:
                business_fee_bands = data.get("business_fee_bands", [])
                if business_fee_bands:
                    for band in business_fee_bands:
                        BusinessFeeBand.objects.update_or_create(
                            business_fee=business_fee,
                            lower_bound=band["lower_bound"],
                            defaults={
                                "fee_type": band.get(
                                    "fee_type", BandedFeeType.Fixed.value
                                ),
                                "amount": band.get("amount", 0),
                                "upper_bound": band.get("upper_bound", 0),
                                "upper_bound_infinite": band.get(
                                    "upper_bound_infinite", False
                                ),
                            },
                        )

    @staticmethod
    def create_provider_fee(data):
        with transaction.atomic():
            provider_fee, _ = ProviderFee.objects.update_or_create(
                provider=data["provider"],
                product=data["product"],
                service=data["service"],
                defaults={
                    "fee_type": data["fee_type"],
                    "amount": data.get("amount", 0),
                    "cap_amount": data.get("cap_amount", 0),
                },
            )

            if data["fee_type"] == FeeType.Banded.value:
                business_fee_bands = data.get("provider_fee_bands", [])
                if business_fee_bands:
                    for band in business_fee_bands:
                        ProviderFeeBand.objects.update_or_create(
                            provider_fee=provider_fee,
                            lower_bound=band["lower_bound"],
                            defaults={
                                "fee_type": band.get(
                                    "fee_type", BandedFeeType.Fixed.value
                                ),
                                "amount": band.get("amount", 0),
                                "upper_bound": band.get("upper_bound", 0),
                                "upper_bound_infinite": band.get(
                                    "upper_bound_infinite", False
                                ),
                            },
                        )

    @staticmethod
    def create_general_fee(data):
        with transaction.atomic():
            general_fee, _ = GeneralFee.objects.update_or_create(
                product=data["product"],
                service=data["service"],
                defaults={
                    "fee_type": data["fee_type"],
                    "amount": data.get("amount", 0),
                    "cap_amount": data.get("cap_amount", 0),
                },
            )

            if data["fee_type"] == FeeType.Banded.value:
                general_fee_bands = data.get("general_fee_bands", [])
                if general_fee_bands:
                    for band in general_fee_bands:
                        fee_type = band.get("fee_type", BandedFeeType.Fixed.value)
                        GeneralFeeBand.objects.update_or_create(
                            general_fee=general_fee,
                            lower_bound=band["lower_bound"],
                            defaults={
                                "fee_type": fee_type,
                                "amount": band.get("amount", 0),
                                "upper_bound": band.get("upper_bound", 0),
                                "upper_bound_infinite": band.get(
                                    "upper_bound_infinite", False
                                ),
                            },
                        )
