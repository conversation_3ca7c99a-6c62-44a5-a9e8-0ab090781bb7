from decimal import Decimal

from django.utils import timezone
from rest_framework import serializers
from transaction.enums import TransactionClassEnum

from .enums import DisputeStatus, DisputeType
from .models import BusinessMember, Dispute
from .permissions import get_user_business_context
from .services import DisputeCreationService


class BusinessMemberSerializer(serializers.ModelSerializer):
    """Serializer for business team members."""

    user_email = serializers.EmailField(source="user.email", read_only=True)
    user_name = serializers.CharField(source="user.fullname", read_only=True)
    added_by_name = serializers.CharField(source="added_by.fullname", read_only=True)

    class Meta:
        model = BusinessMember
        fields = [
            "id",
            "user_email",
            "user_name",
            "role",
            "is_active",
            "added_by_name",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class DisputeCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating disputes."""

    class Meta:
        model = Dispute
        fields = [
            "transaction_reference",
            "vas_service",
            "amount",
            "charge",
            "stamp_duty",
            "previous_balance",
            "new_balance",
            "transaction_date",
            "merchant_name",
            "message",
        ]

    def validate_vas_service(self, value):
        """Validate VAS service type."""
        valid_services = [choice[0] for choice in TransactionClassEnum.choices()]
        if value not in valid_services:
            raise serializers.ValidationError(
                f"Invalid VAS service. Must be one of: {', '.join(valid_services)}"
            )
        return value

    def validate_amount(self, value):
        """Validate transaction amount."""
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than zero.")
        return value

    def validate(self, attrs):
        """Validate the entire dispute data."""
        # Ensure new_balance calculation is consistent
        expected_new_balance = (
            attrs["previous_balance"]
            - attrs["amount"]
            - attrs["charge"]
            - attrs["stamp_duty"]
        )

        if abs(attrs["new_balance"] - expected_new_balance) > Decimal("0.01"):
            raise serializers.ValidationError(
                "New balance calculation doesn't match the provided values."
            )

        # Check for existing disputes for this transaction
        self._validate_no_existing_dispute(attrs["transaction_reference"])

        return attrs

    def _validate_no_existing_dispute(self, transaction_reference):
        """Check if a dispute already exists for this transaction."""
        request = self.context.get("request")
        if not request or not request.user:
            return

        business, _ = get_user_business_context(request.user)

        if not business:
            return

        # Check for existing disputes for this transaction in the same business
        from .models import Dispute

        existing_dispute = Dispute.objects.filter(
            business=business, transaction_reference=transaction_reference
        ).first()

        if existing_dispute:
            raise serializers.ValidationError(
                {
                    "transaction_reference": f"A dispute already exists for transaction '{transaction_reference}'. "
                    f"Dispute ID: {existing_dispute.id} (Status: {existing_dispute.status})"
                }
            )

    def create(self, validated_data):
        """Create a new dispute."""
        request = self.context.get("request")
        business, role = get_user_business_context(request.user)

        if not business:
            raise serializers.ValidationError(
                "User is not associated with any business."
            )

        validated_data["business"] = business
        validated_data["created_by"] = request.user
        validated_data["status"] = DisputeStatus.PENDING.value

        return super().create(validated_data)


class DisputeCreateFromReferenceSerializer(serializers.Serializer):
    """Serializer for creating disputes using only transaction reference and message."""

    transaction_reference = serializers.CharField(
        max_length=100, help_text="Transaction reference ID to create dispute for"
    )
    message = serializers.CharField(help_text="Description of the dispute/issue")

    def validate_transaction_reference(self, value):
        """Validate that transaction reference exists and belongs to user's business."""
        request = self.context.get("request")
        if not request or not request.user:
            raise serializers.ValidationError("Authentication required.")

        # Get user's business context
        business, _ = get_user_business_context(request.user)
        if not business:
            raise serializers.ValidationError(
                "User is not associated with any business."
            )

        # Check for existing disputes for this transaction
        from .models import Dispute

        existing_dispute = Dispute.objects.filter(
            business=business, transaction_reference=value
        ).first()

        if existing_dispute:
            raise serializers.ValidationError(
                f"A dispute already exists for transaction '{value}'. "
                f"Dispute ID: {existing_dispute.id} (Status: {existing_dispute.status})"
            )

        # Validate transaction exists
        try:
            transaction_info = DisputeCreationService.validate_transaction_reference(
                value, business
            )
            # Store transaction info for use in create method
            self._transaction_info = transaction_info
            return value
        except Exception as e:
            raise serializers.ValidationError(str(e))

    def validate_message(self, value):
        """Validate message content."""
        if len(value.strip()) < 10:
            raise serializers.ValidationError(
                "Message must be at least 10 characters long."
            )
        return value.strip()

    def create(self, validated_data):
        """Create dispute from transaction reference."""
        request = self.context.get("request")

        try:
            # Create dispute data from transaction reference
            dispute_data = DisputeCreationService.create_dispute_from_reference(
                reference=validated_data["transaction_reference"],
                message=validated_data["message"],
                user=request.user,
            )

            # Create the dispute
            dispute = Dispute.objects.create(**dispute_data)
            return dispute

        except Exception as e:
            raise serializers.ValidationError(f"Failed to create dispute: {str(e)}")

    def to_representation(self, instance):
        """Return detailed dispute representation."""
        return DisputeDetailSerializer(instance, context=self.context).data


class DisputeListSerializer(serializers.ModelSerializer):
    """Serializer for listing disputes."""

    created_by_name = serializers.CharField(
        source="created_by.fullname", read_only=True
    )
    business_name = serializers.CharField(source="business.name", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    dispute_type_display = serializers.CharField(
        source="get_dispute_type_display", read_only=True
    )
    vas_service_display = serializers.CharField(
        source="get_vas_service_display", read_only=True
    )
    display_title = serializers.CharField(read_only=True)
    has_attachment = serializers.SerializerMethodField()

    class Meta:
        model = Dispute
        fields = [
            "id",
            "dispute_type",
            "dispute_type_display",
            "display_title",
            "topic",
            "transaction_reference",
            "vas_service",
            "vas_service_display",
            "amount",
            "status",
            "status_display",
            "merchant_name",
            "message",
            "has_attachment",
            "created_by_name",
            "business_name",
            "created_at",
            "updated_at",
        ]

    def get_has_attachment(self, obj):
        """Check if dispute has an attachment."""
        return bool(obj.attachment)


class DisputeDetailSerializer(serializers.ModelSerializer):
    """Serializer for dispute details with status-based timeline information."""

    created_by_name = serializers.CharField(
        source="created_by.fullname", read_only=True
    )
    created_by_email = serializers.EmailField(source="created_by.email", read_only=True)
    business_name = serializers.CharField(source="business.name", read_only=True)
    resolved_by_name = serializers.CharField(
        source="resolved_by.fullname", read_only=True
    )
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    dispute_type_display = serializers.CharField(
        source="get_dispute_type_display", read_only=True
    )
    vas_service_display = serializers.CharField(
        source="get_vas_service_display", read_only=True
    )
    display_title = serializers.CharField(read_only=True)
    attachment_url = serializers.SerializerMethodField()
    timeline = serializers.SerializerMethodField()

    class Meta:
        model = Dispute
        fields = [
            "id",
            "dispute_type",
            "dispute_type_display",
            "display_title",
            "topic",
            "attachment",
            "attachment_url",
            "transaction_reference",
            "vas_service",
            "vas_service_display",
            "amount",
            "charge",
            # "stamp_duty",
            "previous_balance",
            "new_balance",
            "transaction_date",
            "status",
            "status_display",
            "merchant_name",
            "message",
            "created_by_name",
            "created_by_email",
            "business_name",
            "resolved_by_name",
            "resolved_at",
            "resolution_notes",
            "created_at",
            "updated_at",
            "timeline",
        ]

    def get_attachment_url(self, obj):
        """Get the full URL for the attachment if it exists."""
        if obj.attachment:
            request = self.context.get("request")
            if request:
                return request.build_absolute_uri(obj.attachment.url)
            return obj.attachment.url
        return None

    def get_timeline(self, obj):
        """
        Get comprehensive timeline showing all status changes with their associated notes.

        Each status change is shown with:
        - The status name
        - When it was changed to that status
        - Any note that was added during the status change
        """
        timeline = []
        timeline.append(
            {
                "status": DisputeStatus.PENDING.value,
                "status_display": "Pending",
                "changed_at": obj.created_at,
                # "changed_by": obj.created_by.fullname if obj.created_by else None,
                "note": None,  # No note for initial creation
                "is_current": obj.status == DisputeStatus.PENDING.value,
            }
        )

        responses = obj.responses.filter(is_internal_note=False).order_by("created_at")

        # Add each status change to timeline
        for response in responses:
            timeline.append(
                {
                    "status": response.new_status,
                    "status_display": dict(DisputeStatus.choices()).get(
                        response.new_status, response.new_status
                    ),
                    "changed_at": response.created_at,
                    # "changed_by": response.responder.fullname if response.responder else None,
                    "note": response.message if response.has_note else None,
                    "is_current": obj.status == response.new_status,
                }
            )

        # If current status doesn't have a response record (shouldn't happen but safety check)
        current_status_in_timeline = any(
            item["status"] == obj.status for item in timeline
        )
        if not current_status_in_timeline and obj.status != DisputeStatus.PENDING.value:
            timeline.append(
                {
                    "status": obj.status,
                    "status_display": dict(DisputeStatus.choices()).get(
                        obj.status, obj.status
                    ),
                    "changed_at": obj.updated_at,
                    # "changed_by": None,
                    "note": None,
                    "is_current": True,
                }
            )

        return {
            "current_status": obj.status,
            "current_status_display": dict(DisputeStatus.choices()).get(
                obj.status, obj.status
            ),
            "status_changes": timeline,
            # "total_status_changes": len(timeline)
        }


class DisputeUpdateStatusSerializer(serializers.ModelSerializer):
    """Serializer for updating dispute status."""

    class Meta:
        model = Dispute
        fields = ["status", "resolution_notes"]

    def validate_status(self, value):
        """Validate status transition."""
        if self.instance:
            current_status = self.instance.status

            # Define valid status transitions
            valid_transitions = {
                DisputeStatus.PENDING.value: [DisputeStatus.IN_REVIEW.value],
                DisputeStatus.IN_REVIEW.value: [
                    DisputeStatus.RESOLVED.value,
                    DisputeStatus.PENDING.value,
                ],
                DisputeStatus.RESOLVED.value: [],  # No transitions from resolved
            }

            if value not in valid_transitions.get(current_status, []):
                raise serializers.ValidationError(
                    f"Cannot transition from {current_status} to {value}"
                )

        return value

    def update(self, instance, validated_data):
        """Update dispute status."""
        if (
            "status" in validated_data
            and validated_data["status"] == DisputeStatus.RESOLVED.value
        ):
            validated_data["resolved_by"] = self.context["request"].user
            validated_data["resolved_at"] = timezone.now()

        return super().update(instance, validated_data)


class DisputeStatsSerializer(serializers.Serializer):
    """Serializer for dispute statistics."""

    total_disputes = serializers.IntegerField()
    pending_disputes = serializers.IntegerField()
    in_review_disputes = serializers.IntegerField()
    resolved_disputes = serializers.IntegerField()
    business_name = serializers.CharField()
    business_id = serializers.CharField()


class GeneralDisputeCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating general disputes."""

    class Meta:
        model = Dispute
        fields = [
            "topic",
            "message",
            "attachment",
        ]

    def validate_topic(self, value):
        """Validate topic field."""
        if not value or len(value.strip()) < 3:
            raise serializers.ValidationError(
                "Topic must be at least 3 characters long."
            )
        return value.strip()

    def validate_message(self, value):
        """Validate message content."""
        if len(value.strip()) < 10:
            raise serializers.ValidationError(
                "Message must be at least 10 characters long."
            )
        return value.strip()

    def validate_attachment(self, value):
        """Validate file attachment."""
        if value:
            # Check file size (max 10MB)
            if value.size > 10 * 1024 * 1024:
                raise serializers.ValidationError("File size cannot exceed 10MB.")

            # Check file type
            allowed_types = [
                "image/jpeg",
                "image/png",
                "image/gif",
                "image/webp",
                "application/pdf",
                "text/plain",
                "text/csv",
                "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/vnd.ms-excel",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ]

            if (
                hasattr(value, "content_type")
                and value.content_type not in allowed_types
            ):
                raise serializers.ValidationError(
                    "File type not supported. Allowed types: images, PDF, text files, Word documents, Excel files."
                )

        return value

    def create(self, validated_data):
        """Create a new general dispute."""
        request = self.context.get("request")
        business, _ = get_user_business_context(request.user)

        if not business:
            raise serializers.ValidationError(
                "User is not associated with any business."
            )

        validated_data["business"] = business
        validated_data["created_by"] = request.user
        validated_data["dispute_type"] = DisputeType.GENERAL.value
        validated_data["status"] = DisputeStatus.PENDING.value
        validated_data["merchant_name"] = (
            request.user.fullname or business.name or "Unknown"
        )

        return super().create(validated_data)
