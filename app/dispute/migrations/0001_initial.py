# Generated by Django 5.1.7 on 2025-06-22 18:31

from decimal import Decimal

import common.kgs
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AdminTeamMember",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("ADMIN", "ADMIN"),
                            ("CUSTOMER_SUPPORT", "CUSTOMER_SUPPORT"),
                            ("OPERATIONS", "OPERATIONS"),
                            ("RECONCILIATION", "RECONCILIATION"),
                            ("DEVELOPER", "DEVELOPER"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                ("is_active", models.<PERSON>oleanField(db_index=True, default=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="BusinessMember",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("BUSINESS_OWNER", "BUSINESS_OWNER"),
                            ("MERCHANT_ADMIN", "MERCHANT_ADMIN"),
                            ("CUSTOMER_SUPPORT", "CUSTOMER_SUPPORT"),
                            ("OPERATIONS", "OPERATIONS"),
                            ("RECONCILIATION", "RECONCILIATION"),
                            ("DEVELOPER", "DEVELOPER"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                ("is_active", models.BooleanField(db_index=True, default=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Dispute",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "dispute_type",
                    models.CharField(
                        choices=[
                            ("TRANSACTION", "TRANSACTION"),
                            ("GENERAL", "GENERAL"),
                        ],
                        db_index=True,
                        default="TRANSACTION",
                        help_text="Type of dispute (Transaction or General)",
                        max_length=20,
                    ),
                ),
                (
                    "topic",
                    models.CharField(
                        blank=True,
                        help_text="Topic/subject for general disputes",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "attachment",
                    models.FileField(
                        blank=True,
                        help_text="Optional file attachment for disputes",
                        null=True,
                        upload_to="dispute_attachments/%Y/%m/%d/",
                    ),
                ),
                (
                    "transaction_reference",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="Reference ID of the disputed transaction",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "vas_service",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("VIRTUAL_ACCOUNT", "VIRTUAL_ACCOUNT"),
                            ("TRANSFER", "TRANSFER"),
                            ("AIRTIME", "AIRTIME"),
                            ("DATA", "DATA"),
                            ("BETTING", "BETTING"),
                            ("ELECTRICITY", "ELECTRICITY"),
                            ("CABLE_TV", "CABLE_TV"),
                            ("SME_DATA", "SME_DATA"),
                            ("KYC", "KYC"),
                            ("EDUCATION", "EDUCATION"),
                            ("EPIN", "EPIN"),
                            ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                        ],
                        db_index=True,
                        help_text="VAS service type (e.g., Airtime, Electricity)",
                        max_length=30,
                        null=True,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Transaction amount",
                        max_digits=20,
                        null=True,
                    ),
                ),
                (
                    "charge",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Transaction charge/fee",
                        max_digits=20,
                        null=True,
                    ),
                ),
                (
                    "stamp_duty",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Stamp duty charged",
                        max_digits=20,
                        null=True,
                    ),
                ),
                (
                    "previous_balance",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Wallet balance before transaction",
                        max_digits=20,
                        null=True,
                    ),
                ),
                (
                    "new_balance",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Wallet balance after transaction",
                        max_digits=20,
                        null=True,
                    ),
                ),
                (
                    "transaction_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Date and time when the transaction occurred",
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "PENDING"),
                            ("IN_REVIEW", "IN_REVIEW"),
                            ("RESOLVED", "RESOLVED"),
                        ],
                        db_index=True,
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                (
                    "merchant_name",
                    models.CharField(
                        help_text="Name of the business owner/merchant", max_length=255
                    ),
                ),
                (
                    "message",
                    models.TextField(help_text="Description of the dispute/issue"),
                ),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("resolution_notes", models.TextField(blank=True, null=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DisputeAssignment",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(db_index=True, default=True)),
                ("notes", models.TextField(blank=True, null=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DisputeResponse",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "message",
                    models.TextField(help_text="Response message from admin team"),
                ),
                (
                    "previous_status",
                    models.CharField(
                        choices=[
                            ("PENDING", "PENDING"),
                            ("IN_REVIEW", "IN_REVIEW"),
                            ("RESOLVED", "RESOLVED"),
                        ],
                        help_text="Status before this response",
                        max_length=20,
                    ),
                ),
                (
                    "new_status",
                    models.CharField(
                        choices=[
                            ("PENDING", "PENDING"),
                            ("IN_REVIEW", "IN_REVIEW"),
                            ("RESOLVED", "RESOLVED"),
                        ],
                        help_text="Status after this response",
                        max_length=20,
                    ),
                ),
                (
                    "is_internal_note",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this is an internal note (not visible to merchant)",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
