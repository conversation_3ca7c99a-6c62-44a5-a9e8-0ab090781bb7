from common.pagination import LargeDatasetKeySetPagination
from common.serializers import EmptySerializer
from django.db.models import Count, Q, Sum
from drf_spectacular.utils import OpenApiParameter, OpenApiTypes, extend_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from transaction.enums import TransactionModeEnum, TransactionStatusEnum
from transaction.models.virtual_account import VirtualAccountVasTransaction
from virtual_account.models import VirtualAccount
from virtual_account.v1.serializers import (
    VirtualAccountSerializer,
    VirtualAccountTransactionSerializer,
)
from wallet.enums import WalletEnums
from wallet.models import Wallet


class VirtualAccountViewSet(viewsets.ModelViewSet):
    queryset = VirtualAccount.objects.all()
    serializer_class = VirtualAccountSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == "Business_Owner":
            queryset = queryset.filter(
                business=self.request.user.business, is_for_wallet_funding=False
            )
        return queryset

    def create(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @action(
        methods=["GET"],
        detail=False,
        url_path="total-accounts",
        serializer_class=EmptySerializer,
    )
    def total_accounts(self, request):
        return Response(
            {
                "total_accounts": self.get_queryset().count(),
            },
            status=status.HTTP_200_OK,
        )


@extend_schema(tags=["Virtual Account Transactions"])
class VirtualAccountTransactionsViewSet(viewsets.ModelViewSet):
    serializer_class = VirtualAccountTransactionSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = LargeDatasetKeySetPagination
    ordering_fields = ["created_at"]

    def get_queryset(self):
        queryset = VirtualAccountVasTransaction.objects.select_related(
            "business"
        ).values(
            "session_id",
            # Source details
            "source_account_number",
            "source_account_name",
            "source_bank_name",
            "source_bank_code",
            # Recipient details
            "recipient_account_number",
            "recipient_account_name",
            "recipient_bank_name",
            "recipient_bank_code",
            # Transaction details
            "reference",
            "merchant_reference",
            "status",
            "mode",
            "amount",
            "charge",
            "net_amount",
            "narration",
            # Business info from related model
            "business__id",
            "business__name",
            "created_at",
        )
        if self.request.user.role == "Business_Owner":
            queryset = queryset.filter(business=self.request.user.business)

        # Apply bank filter if provided
        bank_name = self.request.GET.get("bank_name", "").lower()
        valid_banks = ["kolomoni", "access", "wema"]
        if bank_name and bank_name in valid_banks:
            queryset = queryset.filter(recipient_bank_name__icontains=bank_name)

        return queryset

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="bank_name",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description="Filter transactions by bank name. Options: kolomoni, access, wema",
                required=False,
                enum=["kolomoni", "access", "wema"],
            ),
        ],
        description="List virtual account transactions with optional bank filtering",
        summary="List Virtual Account Transactions",
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="bank_name",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description="Filter by bank name. Options: kolomoni, access, wema",
                required=False,
                enum=["kolomoni", "access", "wema"],
            ),
        ],
        description="Get virtual account transaction overview with optional bank filtering",
        summary="Virtual Account Transaction Overview",
        responses={
            200: {
                "description": "Transaction overview data",
                "example": {
                    "total_inflows": 26,
                    "total_outflows": 19,
                    "total_inflow_amount": 639272.00,
                    "total_outflow_amount": 559551.00,
                    "bank_name": "KOLOMONI",
                    "filtered_by_bank": True,
                },
            },
            400: {"description": "Invalid bank name provided"},
        },
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="overview",
        serializer_class=EmptySerializer,
    )
    def overview(self, request, *args, **kwargs):
        # Get bank filter parameter
        bank_name = request.GET.get("bank_name", "").lower()

        # Validate bank name if provided
        valid_banks = ["kolomoni", "access", "wema"]
        if bank_name and bank_name not in valid_banks:
            return Response(
                {
                    "error": f"Invalid bank name. Must be one of: {', '.join(valid_banks)}"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Start with base queryset
        queryset = self.get_queryset().filter(
            status=TransactionStatusEnum.SUCCESSFUL.value
        )

        # Apply bank filter if provided
        if bank_name:
            # Filter by recipient bank name (transactions coming into business VAs)
            queryset = queryset.filter(recipient_bank_name__icontains=bank_name)

        # Calculate totals
        totals = queryset.aggregate(
            inflows=Count("id", filter=Q(mode=TransactionModeEnum.CREDIT.value)),
            outflows=Count("id", filter=Q(mode=TransactionModeEnum.DEBIT.value)),
            total_inflow_amount=Sum(
                "amount", filter=Q(mode=TransactionModeEnum.CREDIT.value)
            ),
            total_outflow_amount=Sum(
                "amount", filter=Q(mode=TransactionModeEnum.DEBIT.value)
            ),
        )

        response_data = {
            "total_inflows": totals.get("inflows", 0),
            "total_outflows": totals.get("outflows", 0),
            "total_inflow_amount": float(totals.get("total_inflow_amount") or 0),
            "total_outflow_amount": float(totals.get("total_outflow_amount") or 0),
        }

        # Add bank-specific info if filtered
        if bank_name:
            response_data["bank_name"] = bank_name.upper()
            response_data["filtered_by_bank"] = True
        else:
            response_data["filtered_by_bank"] = False

        return Response(response_data)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="bank_name",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description="Filter by bank name. Options: kolomoni, access, wema",
                required=False,
                enum=["kolomoni", "access", "wema"],
            ),
        ],
        description="Get total virtual account wallet balance with optional bank filtering",
        summary="Virtual Account Total Balance",
        responses={
            200: {
                "description": "Total balance data",
                "example": {
                    "total_balance": 199851.00,
                    "bank_name": "WEMA",
                    "filtered_by_bank": True,
                    "virtual_accounts_count": 3,
                },
            },
            400: {"description": "Invalid bank name provided"},
        },
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="total-balance",
        serializer_class=EmptySerializer,
    )
    def total_balance(self, request):
        # Get bank filter parameter
        bank_name = request.GET.get("bank_name", "").lower()

        # Validate bank name if provided
        valid_banks = ["kolomoni", "access", "wema"]
        if bank_name and bank_name not in valid_banks:
            return Response(
                {
                    "error": f"Invalid bank name. Must be one of: {', '.join(valid_banks)}"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Map bank names to wallet types
        bank_to_wallet_type = {
            "kolomoni": WalletEnums.KOLOMONI_VIRTUAL_ACCOUNT,
            "access": WalletEnums.ACCESS_VIRTUAL_ACCOUNT,
            "wema": WalletEnums.WEMA_VIRTUAL_ACCOUNT,
        }

        # Filter wallets based on bank
        if bank_name:
            wallet_types = [bank_to_wallet_type[bank_name]]
        else:
            wallet_types = WalletEnums().va_wallets()

        wallet_qs = Wallet.objects.filter(
            business=self.request.user.business, type__in=wallet_types
        )

        total_balance = wallet_qs.aggregate(total=Sum("balance"))["total"] or 0

        response_data = {
            "total_balance": float(total_balance),
        }

        # Add bank-specific info if filtered
        if bank_name:
            response_data["bank_name"] = bank_name.upper()
            response_data["filtered_by_bank"] = True

            # Get virtual account count for this bank
            va_count = VirtualAccount.objects.filter(
                business=self.request.user.business, bank_name=bank_name
            ).count()
            response_data["virtual_accounts_count"] = va_count
        else:
            response_data["filtered_by_bank"] = False

        return Response(response_data, status=status.HTTP_200_OK)

    @extend_schema(
        description="Get comprehensive summary for all virtual account banks (KOLOMONI, ACCESS, WEMA)",
        summary="Virtual Account Banks Summary",
        responses={
            200: {
                "description": "Summary data for all banks",
                "example": {
                    "banks_summary": [
                        {
                            "bank_name": "KOLOMONI",
                            "virtual_accounts_count": 3,
                            "total_balance": 163175.00,
                            "total_inflows": 9,
                            "total_outflows": 6,
                            "total_inflow_amount": 188064.00,
                            "total_outflow_amount": 210743.00,
                        }
                    ],
                    "total_virtual_accounts": 9,
                    "total_balance_all_banks": 484257.00,
                },
            }
        },
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="bank-summary",
        serializer_class=EmptySerializer,
    )
    def bank_summary(self, request):
        """Get summary data for all 3 virtual account banks"""
        banks = ["kolomoni", "access", "wema"]
        bank_to_wallet_type = {
            "kolomoni": WalletEnums.KOLOMONI_VIRTUAL_ACCOUNT,
            "access": WalletEnums.ACCESS_VIRTUAL_ACCOUNT,
            "wema": WalletEnums.WEMA_VIRTUAL_ACCOUNT,
        }

        summary_data = []

        for bank in banks:
            # Get virtual account count for this bank
            va_count = VirtualAccount.objects.filter(
                business=self.request.user.business, bank_name=bank
            ).count()

            # Get wallet balance for this bank
            wallet_balance = (
                Wallet.objects.filter(
                    business=self.request.user.business, type=bank_to_wallet_type[bank]
                ).aggregate(total=Sum("balance"))["total"]
                or 0
            )

            # Get transaction totals for this bank
            bank_transactions = self.get_queryset().filter(
                status=TransactionStatusEnum.SUCCESSFUL.value,
                recipient_bank_name__icontains=bank,
            )

            transaction_totals = bank_transactions.aggregate(
                inflows=Count("id", filter=Q(mode=TransactionModeEnum.CREDIT.value)),
                outflows=Count("id", filter=Q(mode=TransactionModeEnum.DEBIT.value)),
                total_inflow_amount=Sum(
                    "amount", filter=Q(mode=TransactionModeEnum.CREDIT.value)
                ),
                total_outflow_amount=Sum(
                    "amount", filter=Q(mode=TransactionModeEnum.DEBIT.value)
                ),
            )

            bank_data = {
                "bank_name": bank.upper(),
                "virtual_accounts_count": va_count,
                "total_balance": float(wallet_balance),
                "total_inflows": transaction_totals.get("inflows", 0),
                "total_outflows": transaction_totals.get("outflows", 0),
                "total_inflow_amount": float(
                    transaction_totals.get("total_inflow_amount") or 0
                ),
                "total_outflow_amount": float(
                    transaction_totals.get("total_outflow_amount") or 0
                ),
            }

            summary_data.append(bank_data)

        return Response(
            {
                "banks_summary": summary_data,
                "total_virtual_accounts": sum(
                    bank["virtual_accounts_count"] for bank in summary_data
                ),
                "total_balance_all_banks": sum(
                    bank["total_balance"] for bank in summary_data
                ),
            },
            status=status.HTTP_200_OK,
        )
