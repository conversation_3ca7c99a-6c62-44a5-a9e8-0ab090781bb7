from common.enums import ProductEnum, ProviderEnum, ServiceEnum
from config.models import DefaultProvider, ProviderSetting
from django.core.cache import cache
from django.db import transaction
from django.utils import timezone
from rest_framework import serializers
from user.v1.serializers import UserMiniSerializer


class DefaultProviderSerializer(serializers.ModelSerializer):

    product = serializers.ChoiceField(choices=ProductEnum.choices())
    service = serializers.ChoiceField(choices=ServiceEnum.choices())
    provider = serializers.ChoiceField(choices=ProviderEnum.choices())

    last_updated_by = UserMiniSerializer(required=False, read_only=True)

    class Meta:
        model = DefaultProvider
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at", "last_updated_at")

    def validate(self, attrs):
        attrs = super().validate(attrs)

        exists = ProviderSetting.objects.filter(
            product=attrs["product"],
            service=attrs["service"],
            provider=attrs["provider"],
            is_active=True,
        ).exists()

        if not exists:
            raise serializers.ValidationError(
                {
                    "provider": (
                        f"No active configuration found for "
                        f"{attrs['service']} / {attrs['product']} / {attrs['provider']}"
                    )
                }
            )
        return attrs

    @transaction.atomic
    def create(self, validated_data):
        user = self.context["user"]
        instance, _ = DefaultProvider.objects.update_or_create(
            product=validated_data["product"],
            service=validated_data["service"],
            defaults={
                "provider": validated_data["provider"],
                "last_updated_at": timezone.now(),
                "last_updated_by": user,
            },
        )
        cache_key = (
            f"default_provider:{validated_data['product']}:{validated_data['service']}"
        )
        cache.delete(cache_key)
        return instance


class ProviderSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProviderSetting
        fields = "__all__"
