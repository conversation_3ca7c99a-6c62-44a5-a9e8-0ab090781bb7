from common.enums import ProductEnum, ProviderEnum, ServiceEnum
from config.exceptions import ProviderNotFoundException
from config.models import DefaultProvider
from django.core.cache import cache


def get_provider(product: ProductEnum, service: ServiceEnum) -> ProviderEnum:
    cache_key = f"default_provider:{product.value}:{service.value}"
    cached_provider = cache.get(cache_key)

    print(product, service, cached_provider)

    if cached_provider:
        return ProviderEnum(cached_provider)

    default_provider = DefaultProvider.objects.filter(
        product=product.value, service=service.value
    ).first()

    if not default_provider:
        raise ProviderNotFoundException()

    cache.set(cache_key, default_provider.provider, timeout=3600)  # Cache for 1 hour
    return ProviderEnum(default_provider.provider)
