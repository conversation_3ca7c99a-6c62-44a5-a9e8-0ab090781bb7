from common.enums import ProviderEnum
from config.models import ProviderSetting
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    def handle(self, *args, **options):
        self.stdout.write("Populating providers...")

        created_count = 0
        skipped_count = 0

        for provider in ProviderEnum:
            services = provider.services()
            for product_value, service_value in services:
                obj, created = ProviderSetting.objects.get_or_create(
                    product=product_value,
                    service=service_value,
                    provider=provider.value,
                    defaults={"is_active": True},
                )
                if created:
                    created_count += 1
                else:
                    skipped_count += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"✅ Seed complete! Created: {created_count}, Skipped (already exists): {skipped_count}"
            )
        )
