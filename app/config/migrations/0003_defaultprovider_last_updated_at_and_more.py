# Generated by Django 5.1.7 on 2025-06-24 23:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "config",
            "0002_defaultprovider_providersetting_delete_defaultvender_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="defaultprovider",
            name="last_updated_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="defaultprovider",
            name="last_updated_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="default_provider",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
