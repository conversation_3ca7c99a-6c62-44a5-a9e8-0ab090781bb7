from common.enums import ProductEnum, ProviderEnum, ServiceEnum
from common.models import AuditableModel
from django.db import models


class Bank(AuditableModel):
    name = models.CharField(max_length=100, db_index=True)
    bank_code = models.CharField(max_length=6, db_index=True, null=True, blank=True)
    institution_code = models.CharField(
        max_length=6, db_index=True, null=True, blank=True
    )

    def __str__(self):
        return self.name

    class Meta:
        ordering = ("name",)


class ProviderSetting(AuditableModel):
    """
    Represents the configuration mapping between a product, a vendor, and a service provider.

    This model is used to define which service providers (e.g., MTN, IKEDC, DStv) are accessible
    through which vendors (e.g., Sonite, Shago) for a given product type (e.g., airtime, electricity).

    Attributes:
        product (str): The type of product being offered (e.g., "airtime", "electricity").
        service (str): The specific service available through the vendor (e.g., "MTN", "DSTV").
        provider (str): The vendor we interact with to access the services (e.g., "Sonite", "Shago").
        is_active (bool): Whether the provider configuration is currently active.
    """

    product = models.CharField(max_length=50, choices=ProductEnum.choices())
    service = models.CharField(max_length=50, choices=ServiceEnum.choices())
    provider = models.CharField(max_length=50, choices=ProviderEnum.choices())
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ("provider",)
        unique_together = (
            "product",
            "service",
            "provider",
        )

    def __str__(self):
        return f"{self.product} - {self.service} - {self.provider}"


class DefaultProvider(AuditableModel):
    """
    Represents the default provider configuration for a given product.

    This model defines the primary (default) service provider to use for a specific product and vendor.
    It is particularly useful for determining the active route for service delivery when multiple providers
    are available for the same product.

    Unlike `ProviderSettings`, which lists all available provider options per vendor, this model identifies
    the preferred or default provider configuration to use.

    Attributes:
        product (str): The type of product (e.g., "airtime", "electricity").
        service (str): The default service provider for the product (e.g., "MTN", "DSTV").
        provider (str): The vendor used to access the service (e.g., "Sonite", "Shago").
    """

    product = models.CharField(max_length=50, choices=ProductEnum.choices())
    service = models.CharField(max_length=50, choices=ServiceEnum.choices())
    provider = models.CharField(max_length=50, choices=ServiceEnum.choices())
    last_updated_at = models.DateTimeField(null=True, blank=True)
    last_updated_by = models.ForeignKey(
        "user.User",
        on_delete=models.SET_NULL,
        null=True,
        related_name="default_provider",
    )

    class Meta:
        ordering = ("provider",)

    def __str__(self):
        return f"{self.product} - {self.provider} - {self.service}"
