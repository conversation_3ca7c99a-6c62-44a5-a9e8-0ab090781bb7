from console.v1.views import (
    AdminBusinessChangeRequestViewSet,
    AdminBusinessManagementViewSet,
    ProvidersViewSet,
    SystemVASProductViewSet,
)
from django.urls import include, path
from rest_framework.routers import DefaultRouter

app_name = "console"

router = DefaultRouter()
router.register(
    "business", AdminBusinessManagementViewSet, basename="business-management"
)
router.register(
    "change-requests",
    AdminBusinessChangeRequestViewSet,
    basename="business-change-requests",
)
router.register("vas-products", SystemVASProductViewSet, basename="system-vas-products")
router.register("providers", ProvidersViewSet, basename="providers")

urlpatterns = [
    path("", include(router.urls)),
]
