name: "sagecloud-api"

services:
  db:
    image: postgres:17-alpine
    platform: linux/amd64
    ports:
      - '47432:5432'
    env_file:
      - ./.env
    healthcheck:
      test: [ "CMD", "pg_isready" ]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - db-data:/var/lib/postgresql/data

  api:
    build:
      context: .
      dockerfile: docker/api/Dockerfile
    image: profmcdan/sagecloud-api
    # command: daphne -b 0.0.0.0 -p 60000 core.asgi:application
    command: python3 manage.py runserver 0.0.0.0:47000
    volumes:
      - ./app:/app
    expose:
      - "47000"
    scale: 1
    env_file:
      - ./.env
    environment:
      - DJANGO_SETTINGS_MODULE=core.settings
    restart: always
    depends_on:
      - db
      - redis

  nginx:
    image: nginx:latest
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api
    ports:
      - "47001:47001"

  redis:
    image: redis:7-alpine
    platform: linux/amd64
    ports:
      - "47379:6379"
    volumes:
      - redis-data:/data
    env_file:
      - ./.env

  redis-commander:
    image: rediscommander/redis-commander:latest
    platform: linux/amd64
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=root
      - HTTP_PASSWORD=qwerty
      - REDIS_DB=0
    ports:
      - "47081:8081"
    depends_on:
      - redis

  dashboard:
    build:
      context: .
      dockerfile: docker/celery/Dockerfile-celery
    command: celery --broker=${CELERY_BROKER_URL} flower --port=5555
    ports:
      - "47555:5555"
    env_file:
      - ./.env
    depends_on:
      - api
      - redis

  # test:
  #   build:
  #     context: .
  #     dockerfile: docker/test/Dockerfile-test
  #   container_name: api-test-runner
  #   command: pytest
  #   volumes:
  #     - ./app:/app
  #   env_file:
  #     - ./.env
  #   environment:
  #     DJANGO_SETTINGS_MODULE: core.settings_test
  #   restart: no
  #   depends_on:
  #     - db
  #     - redis

volumes:
  db-data:
  redis-data:
